/**
 * OTA策略统一配置 - 合并版本
 * 
 * 设计目标：
 * - 将所有OTA策略合并到单一文件中
 * - 保持原有的静态方法调用接口
 * - 简化策略文件管理
 * 
 * <AUTHOR>
 * @version 1.0.0 (Unified Strategies)
 */

(function() {
    'use strict';

    /**
     * Fliggy渠道策略
     * 保持原有接口完全不变
     */
    class FliggyOTAStrategy {
        /**
         * 获取策略的渠道名称
         */
        static getChannelName() {
            return 'fliggy';
        }

        /**
         * 获取字段级提示词片段
         * 保持原有接口和实现完全不变
         */
        static getFieldPromptSnippets(_ctx = {}) {
            return {
                // 渠道名称固定返回
                ota: '渠道识别：请识别这是飞猪(Fliggy)渠道的订单，输出JSON时ota字段请设置为"Fliggy"。',
                // 价格与车型ID映射片段
                ota_price: '【关键价格换算规则 - 必须严格执行】：\n1. 价格识别：按优先级提取价格 "商家实收" > "总价格" > "实付" 等字段的数值\n2. 地区判定（非常重要）：\n   - 新加坡地区标识：Universal Studios Singapore, Changi Airport, Singapore, 樟宜机场, 新加坡, 圣淘沙\n   - 马来西亚地区标识：KLIA, KLIA2, Kuala Lumpur, KL, Subang, 吉隆坡\n3. 换算公式（必须使用正确公式）：\n   - 如果地点包含新加坡标识 → 最终价格 = 原始价格 × 0.84 × 0.2\n   - 如果地点包含马来西亚标识 → 最终价格 = 原始价格 × 0.84 × 0.615\n4. 计算示例（请参照执行）：\n   - 新加坡：230元 × 0.84 × 0.2 = 38.64 MYR\n   - 马来西亚：230元 × 0.84 × 0.615 = 118.71 MYR\n5. 输出要求：保留两位小数，必须输出换算后的最终价格',
                car_type_id: '车型ID映射（根据中文车型名称）：请从订单文本中识别车型名称，然后按以下映射返回对应ID：经济型/舒适型/五座→5；经济型/舒适型/七座→35；商务七座/商务型七座→31；豪华七座/豪华型七座→32；商务九座/商务型九座→20；中巴/小巴→24。输出JSON时请设置car_type_id为上述ID之一的整数，若无法识别车型名称则返回null，不要猜测或输出名称。',
                // 新增：日期和地区字段强化
                pickup_date: 'Fliggy日期提取：**必须从订单文本中提取具体日期**，如"2025-08-11"等完整格式，转换为YYYY-MM-DD格式输出。',
                driving_region_id: '【地区识别强化规则】：\n- 新加坡地区（driving_region_id=5）：Universal Studios, Changi Airport, Singapore, 樟宜, 新加坡, 圣淘沙等\n- 马来西亚地区：KLIA(1), KLIA2, KL, Subang(1), Penang(2), Johor(3), Sabah(4), Malacca(12)\n- 当前订单地点包含"Universal Studios"和"Changi Airport"时，必须设置driving_region_id=5(新加坡)\n- 特别注意：斗湖机场→4(Sabah)，不可返回null',
                // 字段完整性确保
                pickup_location: '上车地点：必须从订单中提取完整地点名称，保持原始描述准确性。',
                dropoff_location: '下车地点：必须从订单中提取完整地点名称，保持原始描述准确性。'
            };
        }

        /**
         * 统一车型映射（名称→ID）
         */
        static getVehicleIdMapping() {
            return {
                '经济型': 5, '舒适型': 5, '五座': 5,
                '经济型七座': 35, '舒适型七座': 35, '七座': 35,
                '商务七座': 31, '商务型七座': 31,
                '豪华七座': 32, '豪华型七座': 32,
                '商务九座': 20, '商务型九座': 20,
                '中巴': 24, '小巴': 24
            };
        }

        /**
         * 价格计算规则
         */
        static calculatePrice(basePrice, region = 'malaysia') {
            const factor = region === 'singapore' ? 0.84 * 0.2 : 0.84 * 0.615;
            return Math.round(basePrice * factor * 100) / 100;
        }
    }

    /**
     * JingGe渠道策略
     * 保持原有接口完全不变
     */
    class JingGeOTAStrategy {
        /**
         * 获取策略的渠道名称
         */
        static getChannelName() {
            return 'jingge';
        }

        /**
         * 获取字段级提示词片段
         * 保持原有接口和实现完全不变
         */
        static getFieldPromptSnippets(_ctx = {}) {
            return {
                // 渠道名称固定返回
                ota: '渠道识别：请识别这是JingGe商铺渠道的订单，输出JSON时ota字段请设置为"Jing Ge"。',
                // 价格、车型ID、联系方式、订单号
                ota_price: '价格识别与换算：若为JingGe商铺订单，最终价格=基础价×0.615；保留两位小数，明确输出最终价；无法确定时仅输出基础价并标注原因，不要猜测。',
                car_type_id: '车型ID映射（根据中文车型名称）：请从订单文本中识别车型名称，然后按以下映射返回对应ID：经济型/舒适型/五座→5；经济型/舒适型/七座→35；商务七座/商务型七座→31；豪华七座/豪华型七座→32；商务九座/商务型九座→20；中巴/小巴→24。输出JSON时请设置car_type_id为上述ID之一的整数，若无法识别车型名称则返回null，不要猜测或输出名称。',
                customer_contact: '若订单未提供手机号，可临时使用订单号(ota_reference_number)作为联系标识填充customer_contact字段；若存在手机号，请保持原值，不要覆盖。',
                ota_reference_number: '订单号识别：请从文本中抽取明确的订单编号；一般为纯数字组合，若无可靠线索，请返回null，不要凭空生成。',
                pickup_date: 'JingGe日期解析规则：输入格式如10/8、01/07、8.13等，默认为2025年；以当前日期为判断基准，选择月份前后最近的日期。例如：当前8月15日，输入10/8解析为2025-10-08，输入01/07解析为2025-01-07（下一年较近），输入8.13解析为2025-08-13。输出YYYY-MM-DD格式。'
            };
        }

        /**
         * 统一车型映射（名称→ID）
         */
        static getVehicleIdMapping() {
            return {
                '经济型': 5, '舒适型': 5, '五座': 5,
                '经济型七座': 35, '舒适型七座': 35, '七座': 35,
                '商务七座': 31, '商务型七座': 31,
                '豪华七座': 32, '豪华型七座': 32,
                '商务九座': 20, '商务型九座': 20,
                '中巴': 24, '小巴': 24
            };
        }

        /**
         * 价格计算规则
         */
        static calculatePrice(basePrice) {
            return Math.round(basePrice * 0.615 * 100) / 100;
        }

        /**
         * JingGe日期解析规则
         * 输入格式: 10/8, 01/07, 8.13 等
         * 默认年份: 2025
         * 逻辑: 以当前日期为基准，选择前后最近的日期
         */
        static parseJingGeDate(dateStr, currentDate = new Date()) {
            if (!dateStr) return null;
            
            try {
                // 清理输入
                const cleaned = dateStr.trim();
                let month, day;
                
                // 匹配各种分隔符格式: 10/8, 01/07, 8.13, 12-25
                const patterns = [
                    /^(\d{1,2})[\/\.\-](\d{1,2})$/,  // MM/DD, MM.DD, MM-DD
                    /^(\d{1,2})月(\d{1,2})日?$/,      // 中文格式
                ];
                
                let matched = false;
                for (const pattern of patterns) {
                    const match = cleaned.match(pattern);
                    if (match) {
                        month = parseInt(match[1]);
                        day = parseInt(match[2]);
                        matched = true;
                        break;
                    }
                }
                
                if (!matched || month < 1 || month > 12 || day < 1 || day > 31) {
                    return null;
                }
                
                // 当前日期信息
                const currentYear = currentDate.getFullYear();
                const currentMonth = currentDate.getMonth() + 1;
                const currentDay = currentDate.getDate();
                
                // 默认使用2025年，但根据当前日期调整
                let targetYear = 2025;
                
                // 如果当前已经是2025年之后，使用当前年份
                if (currentYear >= 2025) {
                    targetYear = currentYear;
                    
                    // 距离判断逻辑：选择前后最近的月份
                    const currentDate_thisYear = new Date(currentYear, month - 1, day);
                    const currentDate_nextYear = new Date(currentYear + 1, month - 1, day);
                    
                    const diffThisYear = Math.abs(currentDate_thisYear.getTime() - currentDate.getTime());
                    const diffNextYear = Math.abs(currentDate_nextYear.getTime() - currentDate.getTime());
                    
                    // 如果下一年更近，使用下一年
                    if (diffNextYear < diffThisYear) {
                        targetYear = currentYear + 1;
                    }
                } else {
                    // 当前年份小于2025，使用2025年作为基准进行距离判断
                    const date2025 = new Date(2025, month - 1, day);
                    const date2026 = new Date(2026, month - 1, day);
                    
                    const diff2025 = Math.abs(date2025.getTime() - currentDate.getTime());
                    const diff2026 = Math.abs(date2026.getTime() - currentDate.getTime());
                    
                    targetYear = diff2026 < diff2025 ? 2026 : 2025;
                }
                
                // 构造最终日期并验证
                const resultDate = new Date(targetYear, month - 1, day);
                if (isNaN(resultDate.getTime())) {
                    return null;
                }
                
                // 返回 YYYY-MM-DD 格式
                return `${targetYear}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
                
            } catch (error) {
                console.error('JingGe日期解析失败:', error);
                return null;
            }
        }
    }

    /**
     * 统一策略配置对象
     * 提供统一的策略访问接口
     */
    const UnifiedOTAStrategies = {
        fliggy: FliggyOTAStrategy,
        jingge: JingGeOTAStrategy,
        
        /**
         * 获取指定策略
         */
        getStrategy(channel) {
            return this[channel] || null;
        },

        /**
         * 获取所有可用策略
         */
        getAllStrategies() {
            return {
                fliggy: FliggyOTAStrategy,
                jingge: JingGeOTAStrategy
            };
        },

        /**
         * 检查策略是否存在
         */
        hasStrategy(channel) {
            return channel in this && typeof this[channel] === 'function';
        }
    };

    // 暴露到全局作用域（保持向后兼容）
    window.FliggyOTAStrategy = FliggyOTAStrategy;
    window.JingGeOTAStrategy = JingGeOTAStrategy;
    
    // 新的统一接口
    window.OTA = window.OTA || {};
    window.OTA.strategies = UnifiedOTAStrategies;

    console.log('✅ 统一OTA策略配置已加载');

})();