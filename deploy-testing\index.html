<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA订单处理系统 - 测试环境</title>
    <link rel="stylesheet" href="css/main.css">
    
    
    <style>
        .env-banner {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: #2196F3;
            color: white;
            padding: 5px 10px;
            text-align: center;
            font-size: 12px;
            z-index: 10000;
        }
        body { margin-top: 30px; }
    </style>
    
</head>
<body>
    
    <div class="env-banner">
        🔧 测试环境 - Linus Torvalds式重构版本
    </div>
    

    <div id="app">
        <!-- 登录面板 -->
        <div id="loginPanel" class="login-panel">
            <div class="login-card">
                <h2>🚗 OTA订单处理系统</h2>
                <p>Linus Torvalds式重构版本</p>
                <form id="loginForm">
                    <div class="form-group">
                        <input type="email" id="email" placeholder="邮箱" required>
                    </div>
                    <div class="form-group">
                        <input type="password" id="password" placeholder="密码" required>
                    </div>
                    <button type="submit" class="btn btn-primary">登录</button>
                </form>
            </div>
        </div>

        <!-- 主工作区 -->
        <div id="workspace" class="hidden">
            <header class="app-header">
                <h1>OTA订单处理系统</h1>
                <div class="user-info">
                    <span id="currentUser"></span>
                    <button id="logoutBtn" class="btn btn-outline">退出登录</button>
                </div>
            </header>

            <main class="main-content">
                <div class="input-section">
                    <h3>订单信息输入</h3>
                    <textarea id="orderInput" 
                              placeholder="请粘贴订单信息，系统将自动解析..."
                              rows="6"></textarea>
                </div>

                <div class="form-section">
                    <h3>订单详情</h3>
                    <form id="orderForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="customerName">客户姓名</label>
                                <input type="text" id="customerName" required>
                            </div>
                            <div class="form-group">
                                <label for="customerContact">联系电话</label>
                                <input type="tel" id="customerContact" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="pickup">接送地点</label>
                                <input type="text" id="pickup" required>
                            </div>
                            <div class="form-group">
                                <label for="dropoff">目的地</label>
                                <input type="text" id="dropoff" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="pickupDate">接送日期</label>
                                <input type="date" id="pickupDate" required>
                            </div>
                            <div class="form-group">
                                <label for="pickupTime">接送时间</label>
                                <input type="time" id="pickupTime" required>
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">创建订单</button>
                            <button type="button" id="resetBtn" class="btn btn-secondary">重置</button>
                        </div>
                    </form>
                </div>
            </main>
        </div>

        <!-- 多订单面板 -->
        <div id="multiOrderPanel" class="hidden">
            <div class="multi-order-container">
                <h3>多订单处理</h3>
                <div id="multiOrderList"></div>
                <div class="multi-order-actions">
                    <button onclick="window.ota.multiOrder.createSelected()" class="btn btn-primary">创建选中</button>
                    <button onclick="window.ota.multiOrder.createAll()" class="btn btn-success">创建全部</button>
                    <button onclick="window.ota.multiOrder.cancel()" class="btn btn-secondary">取消</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 性能监控 (非生产环境) -->
    
    <div id="perfMonitor" style="position: fixed; bottom: 10px; right: 10px; 
                                  background: rgba(0,0,0,0.8); color: white; 
                                  padding: 8px 12px; border-radius: 4px; 
                                  font-family: monospace; font-size: 11px;">
        <div>Environment: testing</div>
        <div>Load: <span id="loadTime">-</span>ms</div>
        <div>Memory: <span id="memoryUsage">-</span>MB</div>
    </div>
    

    <!-- 脚本加载 -->
    <script>
        const ENVIRONMENT = 'testing';
        const DEBUG = true;
        const startTime = performance.now();
        
        console.log("🚀 Starting Linus refactor system...");
    </script>
    
    
    <script src="core.js"></script>
    <script src="compatibility-bridge.js"></script>
    <script src="main-simple.js"></script>
    
    
    
    <script>
        // 性能监控
        window.addEventListener('load', () => {
            const loadTime = performance.now() - startTime;
            document.getElementById('loadTime').textContent = loadTime.toFixed(0);
            
            if (performance.memory) {
                const memory = (performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(1);
                document.getElementById('memoryUsage').textContent = memory;
            }
            
            
            console.log("✅ System loaded in", loadTime.toFixed(1) + "ms");
            console.log("🏗️ Environment:", ENVIRONMENT);
            console.log("🔧 Debug mode:", DEBUG);
            
        });
    </script>
    
</body>
</html>