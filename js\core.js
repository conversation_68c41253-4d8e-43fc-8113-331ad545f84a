/**
 * 核心OTA系统 - <PERSON><PERSON> Torvalds式重构版本
 * 
 * 这个文件体现了"好品味"原则：
 * - 消除不必要的抽象层
 * - 直接解决问题，不创造问题
 * - 简单、直接、高效
 * 
 * "我是个该死的实用主义者。" - <PERSON><PERSON> Torvalds
 */

'use strict';

// 简化的全局对象 - 消除服务定位器地狱
window.ota = {
    config: {
        api: {
            baseURL: 'https://gomyhire.com.my/api',
            timeout: 30000
        },
        gemini: {
            endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent',
            timeout: 45000
        }
    },
    
    // 合并的订单历史 - 简化版本 (替代OrderHistoryManager)
    history: {
        storageKey: 'ota_order_history',
        maxSize: 1000,
        
        save(order, userEmail) {
            const key = `${this.storageKey}_${userEmail}`;
            const history = JSON.parse(localStorage.getItem(key) || '[]');
            
            // 添加时间戳
            order.created_at = new Date().toISOString();
            order.id = order.id || Date.now().toString();
            
            // 添加到开头，保持最新的在前
            history.unshift(order);
            
            // 限制数量
            if (history.length > this.maxSize) {
                history.splice(this.maxSize);
            }
            
            localStorage.setItem(key, JSON.stringify(history));
            console.log(`✅ 订单已保存到历史: ${order.id}`);
        },
        
        get(userEmail, limit = 50) {
            const key = `${this.storageKey}_${userEmail}`;
            const history = JSON.parse(localStorage.getItem(key) || '[]');
            return history.slice(0, limit);
        },
        
        clear(userEmail) {
            const key = `${this.storageKey}_${userEmail}`;
            localStorage.removeItem(key);
        }
    },
    
    // 合并的酒店数据 - 核心版本 (替代3个酒店数据文件)
    hotels: {
        data: [
            // 吉隆坡主要酒店
            { name: '香格里拉酒店', english: 'Shangri-La Hotel Kuala Lumpur', region: 'KL' },
            { name: '希尔顿酒店', english: 'Hilton Kuala Lumpur', region: 'KL' },
            { name: '万豪酒店', english: 'JW Marriott Kuala Lumpur', region: 'KL' },
            { name: '丽思卡尔顿酒店', english: 'The Ritz-Carlton Kuala Lumpur', region: 'KL' },
            { name: '洲际酒店', english: 'InterContinental Kuala Lumpur', region: 'KL' },
            // 槟城主要酒店  
            { name: '槟城香格里拉酒店', english: 'Shangri-La Hotel Penang', region: 'Penang' },
            { name: '东方大酒店', english: 'Eastern & Oriental Hotel', region: 'Penang' },
            // 马六甲主要酒店
            { name: '马六甲香格里拉酒店', english: 'Shangri-La Hotel Malacca', region: 'Malacca' }
        ],
        
        find(query) {
            const q = query.toLowerCase();
            return this.data.filter(hotel => 
                hotel.name.includes(q) || 
                hotel.english.toLowerCase().includes(q)
            );
        },
        
        normalize(hotelName) {
            const found = this.find(hotelName);
            return found.length > 0 ? found[0].english : hotelName;
        }
    },
    
    // 渠道检测 - 本地处理，快速响应
    channelDetector: {
        patterns: {
            fliggy: /订单编号[：:\s]*\d{19}/,
            jingge: /jingge|jinggeshop|精格|精格商铺/i,
            reference: /^(CD|CT|KL|KK)[A-Z0-9]{6,12}$/i
        },
        
        detect(text) {
            const results = [];
            
            // Fliggy检测
            if (this.patterns.fliggy.test(text)) {
                results.push({ channel: 'fliggy', confidence: 0.9 });
            }
            
            // JingGe检测
            if (this.patterns.jingge.test(text)) {
                results.push({ channel: 'jingge', confidence: 0.85 });
            }
            
            // 参考号检测
            if (this.patterns.reference.test(text)) {
                results.push({ channel: 'generic', confidence: 0.7 });
            }
            
            return results.length > 0 ? results[0] : null;
        }
    },
    
    // Gemini API调用器 - 直接调用，无适配器废话
    gemini: {
        async parseOrder(text, channel = null) {
            const prompt = this.buildPrompt(text, channel);
            // 🚀 修复：统一API密钥配置，优先使用gemini-caller的配置
            const apiKey = window.OTA?.geminiCaller?.config?.apiKey || 
                          window.GEMINI_API_KEY || 
                          'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s';
            
            if (!apiKey) {
                throw new Error('Gemini API密钥未配置');
            }
            
            try {
                const response = await fetch(`${window.ota.config.gemini.endpoint}?key=${apiKey}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        contents: [{ parts: [{ text: prompt }] }],
                        generationConfig: {
                            temperature: 0.1,
                            maxOutputTokens: 4096
                        }
                    }),
                    signal: AbortSignal.timeout(window.ota.config.gemini.timeout)
                });
                
                if (!response.ok) {
                    throw new Error(`Gemini API错误: ${response.status}`);
                }
                
                const data = await response.json();
                const resultText = data.candidates?.[0]?.content?.parts?.[0]?.text;
                
                if (!resultText) {
                    throw new Error('Gemini返回空结果');
                }
                
                return JSON.parse(resultText);
                
            } catch (error) {
                console.error('Gemini API调用失败:', error);
                throw error;
            }
        },
        
        buildPrompt(text, channel) {
            let basePrompt = `请解析以下订单文本，返回JSON格式的订单信息：\n\n${text}\n\n`;
            
            if (channel) {
                basePrompt += `检测到的渠道: ${channel.channel}\n`;
            }
            
            basePrompt += `
要求：
1. 如果包含多个订单，设置 "isMultiOrder": true，并在 "orders" 数组中返回所有订单
2. 如果只有一个订单，设置 "isMultiOrder": false，在 "order" 中返回订单信息
3. 必须提取：customer_name, customer_contact, pickup, destination, date, time, passenger_number
4. 可选字段：customer_email, ota_reference_number, flight_info, sub_category_id, driving_region_id
5. 酒店名称自动标准化为英文名称
6. 时间格式统一为HH:mm，日期格式为YYYY-MM-DD

返回格式示例：
单订单: {"isMultiOrder": false, "order": {...}}
多订单: {"isMultiOrder": true, "orders": [{...}, {...}]}`;
            
            return basePrompt;
        },
        
        // 图片分析功能
        async analyzeImage(base64Image) {
            // 🚀 修复：统一API密钥配置，与parseOrder保持一致
            const apiKey = window.OTA?.geminiCaller?.config?.apiKey || 
                          window.GEMINI_API_KEY || 
                          'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s';
            if (!apiKey) throw new Error('Gemini API key required');
            
            const prompt = `请分析这张图片中的订单信息，提取所有相关的订单详情并返回JSON格式。
包括：客户姓名、联系方式、接送地点、目的地、日期时间、人数等信息。`;
            
            try {
                const response = await fetch(`${window.ota.config.gemini.endpoint}?key=${apiKey}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        contents: [{
                            parts: [
                                { text: prompt },
                                { 
                                    inline_data: {
                                        mime_type: "image/jpeg",
                                        data: base64Image
                                    }
                                }
                            ]
                        }],
                        generationConfig: {
                            temperature: 0.1,
                            maxOutputTokens: 4096
                        }
                    }),
                    signal: AbortSignal.timeout(window.ota.config.gemini.timeout)
                });
                
                if (!response.ok) {
                    throw new Error(`Gemini vision API error: ${response.status}`);
                }
                
                const data = await response.json();
                const resultText = data.candidates?.[0]?.content?.parts?.[0]?.text;
                
                if (!resultText) {
                    throw new Error('Empty vision response');
                }
                
                return JSON.parse(resultText);
                
            } catch (error) {
                console.error('Gemini vision API failed:', error);
                throw error;
            }
        },
        
        // 多订单检测和分割验证
        async detectAndSplitMultiOrdersWithVerification(text, options = {}) {
            // 首先使用本地检测
            const hasMultiOrder = window.ota.multiOrder.detect(text);
            
            if (!hasMultiOrder) {
                return { isMultiOrder: false, orders: [] };
            }
            
            // 使用Gemini进行详细解析和验证
            try {
                const result = await this.parseOrder(text);
                
                if (result.isMultiOrder && result.orders && result.orders.length > 1) {
                    // 验证每个订单的完整性
                    const validOrders = result.orders.filter(order => 
                        order.customer_name && 
                        order.pickup && 
                        order.destination &&
                        order.date
                    );
                    
                    return {
                        isMultiOrder: validOrders.length > 1,
                        orders: validOrders,
                        totalFound: result.orders.length,
                        validCount: validOrders.length
                    };
                }
                
                return { isMultiOrder: false, orders: [] };
                
            } catch (error) {
                console.error('Multi-order verification failed:', error);
                // 降级到本地检测结果
                return { isMultiOrder: false, orders: [] };
            }
        }
    },
    
    // API服务 - 简化版，只保留核心功能
    api: {
        async request(endpoint, options = {}) {
            const url = `${window.ota.config.api.baseURL}${endpoint}`;
            const token = localStorage.getItem('access_token');
            
            const config = {
                method: options.method || 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    ...(token && { 'Authorization': `Bearer ${token}` }),
                    ...options.headers
                },
                signal: AbortSignal.timeout(window.ota.config.api.timeout)
            };
            
            if (options.body) {
                config.body = JSON.stringify(options.body);
            }
            
            try {
                const response = await fetch(url, config);
                
                if (!response.ok) {
                    throw new Error(`API错误: ${response.status} ${response.statusText}`);
                }
                
                return await response.json();
                
            } catch (error) {
                console.error(`API请求失败 [${endpoint}]:`, error);
                throw error;
            }
        },
        
        // 核心API方法 - 去掉所有适配器废话
        async createOrder(orderData) {
            // 数据预处理和验证
            const processedData = this.preprocessOrderData(orderData);
            
            return this.request('/v1/order-jobs', {
                method: 'POST',
                body: processedData
            });
        },
        
        // 订单数据预处理
        preprocessOrderData(orderData) {
            // 标准化酒店名称
            if (orderData.pickup) {
                orderData.pickup = window.ota.hotels.normalize(orderData.pickup);
            }
            if (orderData.destination) {
                orderData.destination = window.ota.hotels.normalize(orderData.destination);
            }
            
            // 设置默认值
            const processed = {
                customer_name: orderData.customer_name || '',
                customer_contact: orderData.customer_contact || '',
                customer_email: orderData.customer_email || '',
                pickup: orderData.pickup || '',
                destination: orderData.destination || '',
                date: orderData.date || '',
                time: orderData.time || '',
                passenger_number: parseInt(orderData.passenger_number) || 1,
                luggage_number: parseInt(orderData.luggage_number) || 0,
                ota_reference_number: orderData.ota_reference_number || '',
                flight_info: orderData.flight_info || '',
                sub_category_id: orderData.sub_category_id || 1,
                driving_region_id: orderData.driving_region_id || 1,
                car_type_id: orderData.car_type_id || 1,
                ...orderData // 保留其他字段
            };
            
            return processed;
        },
        
        // 批量创建订单（支持重试）
        async batchCreateOrders(orders, options = {}) {
            const results = [];
            const { maxRetries = 3, delayBetween = 500 } = options;
            
            for (let i = 0; i < orders.length; i++) {
                const order = orders[i];
                let success = false;
                let lastError = null;
                
                // 重试逻辑
                for (let retry = 0; retry <= maxRetries && !success; retry++) {
                    try {
                        if (retry > 0) {
                            console.log(`重试创建订单 ${i + 1} (第${retry}次重试)`);
                            await new Promise(resolve => setTimeout(resolve, delayBetween * retry));
                        }
                        
                        const result = await this.createOrder(order);
                        results.push({ success: true, order, result, retries: retry });
                        success = true;
                        
                        // 保存到历史
                        const userEmail = localStorage.getItem('user_email');
                        if (userEmail) {
                            window.ota.history.save(order, userEmail);
                        }
                        
                    } catch (error) {
                        lastError = error;
                        console.error(`订单 ${i + 1} 创建失败 (尝试${retry + 1}):`, error);
                    }
                }
                
                if (!success) {
                    results.push({ success: false, order, error: lastError, retries: maxRetries });
                }
                
                // 订单间延迟
                if (i < orders.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, delayBetween));
                }
            }
            
            return results;
        },
        
        async getOrderHistory(params = {}) {
            const query = new URLSearchParams(params).toString();
            try {
                // 🚀 修复：优先尝试API调用
                return this.request(`/v1/order-jobs?${query}`);
            } catch (error) {
                console.warn('API调用失败，使用本地历史数据:', error.message);
                // 🚀 修复：API失败时使用本地历史数据作为备选
                const userEmail = localStorage.getItem('user_email');
                if (userEmail) {
                    const localHistory = window.ota.history.get(userEmail, params.limit || 50);
                    console.log(`✅ 从本地获取到 ${localHistory.length} 条历史记录`);
                    return localHistory;
                } else {
                    throw new Error('无法获取订单历史：API不可用且未登录');
                }
            }
        },
        
        // 获取核心数据
        async getCoreData() {
            try {
                const [subCategories, carTypes, regions] = await Promise.all([
                    this.request('/v1/sub-categories'),
                    this.request('/v1/car-types'), 
                    this.request('/v1/driving-regions')
                ]);
                
                return { subCategories, carTypes, regions };
            } catch (error) {
                console.warn('API获取核心数据失败，使用增强默认数据:', error.message);
                // 🚀 修复：返回更完整的默认数据
                return {
                    subCategories: [
                        { id: 1, name: '机场接送', name_en: 'Airport Transfer' },
                        { id: 2, name: '市内包车', name_en: 'City Charter' },
                        { id: 3, name: '跨城接送', name_en: 'Intercity Transfer' }
                    ],
                    carTypes: [
                        { id: 1, name: '标准轿车', name_en: 'Standard Sedan', capacity: 4 },
                        { id: 2, name: '商务车', name_en: 'MPV', capacity: 7 },
                        { id: 3, name: '豪华轿车', name_en: 'Luxury Sedan', capacity: 4 }
                    ],
                    regions: [
                        { id: 1, name: '吉隆坡', name_en: 'Kuala Lumpur', code: 'KL' },
                        { id: 2, name: '槟城', name_en: 'Penang', code: 'PG' },
                        { id: 3, name: '新山', name_en: 'Johor Bahru', code: 'JB' }
                    ]
                };
            }
        },
        
        // 上传图片并分析
        async uploadAndAnalyzeImage(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                
                reader.onload = async (e) => {
                    try {
                        const base64 = e.target.result.split(',')[1]; // 去掉data:image/jpeg;base64,前缀
                        const result = await window.ota.gemini.analyzeImage(base64);
                        resolve(result);
                    } catch (error) {
                        reject(error);
                    }
                };
                
                reader.onerror = () => reject(new Error('文件读取失败'));
                reader.readAsDataURL(file);
            });
        }
    },
    
    // 合并的多订单处理 - 简化版本 (替代MultiOrderCoordinator等5个模块)
    multiOrder: {
        isActive: false,
        orders: [],
        originalText: '',
        
        // 检测多订单 - 简化逻辑
        detect(text) {
            const orderPatterns = [
                /订单[编号]*[：:\s]*[\d\w]+/gi,
                /\d{2}:\d{2}.*?\d{2}:\d{2}/g,  // 时间范围
                /(\d{1,2}月\d{1,2}日|\d{4}-\d{2}-\d{2})/g  // 日期
            ];
            
            const matches = orderPatterns.map(pattern => 
                (text.match(pattern) || []).length
            );
            
            const totalMatches = matches.reduce((a, b) => a + b, 0);
            return totalMatches >= 3; // 3个以上匹配认为是多订单
        },
        
        // 激活多订单模式
        activate(orders, originalText) {
            this.isActive = true;
            this.orders = orders;
            this.originalText = originalText;
            
            console.log(`✅ 多订单模式已激活: ${orders.length}个订单`);
            
            // 直接显示界面
            this.showUI();
        },
        
        // 显示多订单界面 - 简化版本
        showUI() {
            const panel = document.getElementById('multiOrderPanel');
            if (!panel) return;
            
            // 生成订单列表HTML
            const ordersHTML = this.orders.map((order, index) => `
                <div class="multi-order-item" data-index="${index}">
                    <input type="checkbox" checked class="order-checkbox">
                    <div class="order-summary">
                        <strong>${order.customer_name || '未知客户'}</strong>
                        <span>${order.date} ${order.time}</span>
                        <span>${order.pickup} → ${order.destination}</span>
                    </div>
                </div>
            `).join('');
            
            panel.innerHTML = `
                <h3>检测到多个订单 (${this.orders.length})</h3>
                <div class="multi-order-list">${ordersHTML}</div>
                <div class="multi-order-actions">
                    <button onclick="window.ota.multiOrder.createSelected()">创建选中订单</button>
                    <button onclick="window.ota.multiOrder.createAll()">创建全部订单</button>
                    <button onclick="window.ota.multiOrder.cancel()">取消</button>
                </div>
            `;
            
            panel.classList.remove('hidden');
        },
        
        // 创建选中的订单
        async createSelected() {
            const checkboxes = document.querySelectorAll('.order-checkbox:checked');
            const selectedOrders = Array.from(checkboxes).map(cb => {
                const index = parseInt(cb.closest('.multi-order-item').dataset.index);
                return this.orders[index];
            });
            
            await this.batchCreate(selectedOrders);
        },
        
        // 创建所有订单
        async createAll() {
            await this.batchCreate(this.orders);
        },
        
        // 批量创建订单 - 简化版本
        async batchCreate(orders) {
            console.log(`🚀 开始批量创建 ${orders.length} 个订单`);
            
            const results = [];
            
            for (let i = 0; i < orders.length; i++) {
                const order = orders[i];
                
                try {
                    console.log(`📝 创建订单 ${i + 1}/${orders.length}: ${order.customer_name}`);
                    
                    const result = await window.ota.api.createOrder(order);
                    results.push({ success: true, order, result });
                    
                    // 保存到历史
                    const userEmail = localStorage.getItem('user_email');
                    if (userEmail) {
                        window.ota.history.save(order, userEmail);
                    }
                    
                } catch (error) {
                    console.error(`❌ 订单创建失败: ${order.customer_name}`, error);
                    results.push({ success: false, order, error });
                }
                
                // 简单延迟，避免API限制
                if (i < orders.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }
            
            // 显示结果
            this.showResults(results);
        },
        
        // 显示批量创建结果
        showResults(results) {
            const successCount = results.filter(r => r.success).length;
            const failCount = results.length - successCount;
            
            alert(`批量创建完成:\n✅ 成功: ${successCount}\n❌ 失败: ${failCount}`);
            
            // 清理状态
            this.cancel();
        },
        
        // 取消多订单模式
        cancel() {
            this.isActive = false;
            this.orders = [];
            this.originalText = '';
            
            const panel = document.getElementById('multiOrderPanel');
            if (panel) {
                panel.classList.add('hidden');
            }
            
            console.log('✅ 多订单模式已取消');
        }
    },

    // UI管理器 - 直接DOM操作，无适配器
    ui: {
        elements: {},
        
        init() {
            // 缓存常用元素
            this.elements = {
                orderInput: document.getElementById('orderInput'),
                workspace: document.getElementById('workspace'),
                multiOrderPanel: document.getElementById('multiOrderPanel'),
                orderForm: document.getElementById('orderForm')
            };
            
            // 绑定事件
            this.bindEvents();
            
            // 🚀 修复：初始化时加载下拉菜单数据
            this.loadDropdownData();

            // 🚀 新增：实时字段验证初始化
            this.setupRealtimeValidation();
            
            console.log('✅ UI初始化完成');
        },
        
        // 🚀 新增：加载下拉菜单数据
        async loadDropdownData() {
            try {
                console.log('🔄 加载下拉菜单数据...');
                const data = await window.ota.api.getCoreData();
                this.populateDropdowns(data);
                console.log('✅ 下拉菜单数据加载完成');
            } catch (error) {
                console.error('❌ 下拉菜单数据加载失败:', error);
            }
        },
        
        // 🚀 新增：填充下拉菜单
        populateDropdowns(data) {
            // 填充服务类型下拉菜单
            const subCategorySelect = document.getElementById('subCategoryId');
            if (subCategorySelect && data.subCategories) {
                subCategorySelect.innerHTML = '<option value="">请选择服务类型</option>';
                data.subCategories.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.id;
                    option.textContent = item.name;
                    subCategorySelect.appendChild(option);
                });
            }
            
            // 填充车型下拉菜单
            const carTypeSelect = document.getElementById('carTypeId');
            if (carTypeSelect && data.carTypes) {
                carTypeSelect.innerHTML = '<option value="">请选择车型</option>';
                data.carTypes.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.id;
                    option.textContent = `${item.name} (${item.capacity}人)`;
                    carTypeSelect.appendChild(option);
                });
            }
            
            // 填充地区下拉菜单
            const regionSelect = document.getElementById('drivingRegionId');
            if (regionSelect && data.regions) {
                regionSelect.innerHTML = '<option value="">请选择地区</option>';
                data.regions.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.id;
                    option.textContent = item.name;
                    regionSelect.appendChild(option);
                });
            }
            
            console.log('✅ 下拉菜单填充完成', data);
        },
        
        bindEvents() {
            // 订单输入实时分析
            if (this.elements.orderInput) {
                this.elements.orderInput.addEventListener('input', 
                    this.debounce((e) => this.analyzeInput(e.target.value), 1000)
                );
            }
            
            // 图片上传支持
            this.setupImageUpload();
            
            // 订单表单提交
            if (this.elements.orderForm) {
                this.elements.orderForm.addEventListener('submit', (e) => this.handleOrderSubmit(e));
            }
        },

        // 🚀 新增：实时验证监听
        setupRealtimeValidation() {
            const form = this.elements.orderForm;
            if (!form) return;

            // 注入基础样式（一次性）
            if (!document.getElementById('realtime-validation-style')) {
                const style = document.createElement('style');
                style.id = 'realtime-validation-style';
                style.textContent = `
                    .field-error { color:#f44336; font-size:12px; margin-top:4px; line-height:1.2; }
                    .field-warning { color:#ff9800; font-size:12px; margin-top:4px; line-height:1.2; }
                    .input-invalid { border-color:#f44336 !important; }
                    .input-warning { border-color:#ff9800 !important; }
                    .validation-summary { margin-top:10px; font-size:13px; }
                    .validation-summary.error { color:#f44336; }
                    .validation-summary.warning { color:#ff9800; }
                `;
                document.head.appendChild(style);
            }

            // 监听全部可输入字段
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(el => {
                const evt = el.tagName === 'SELECT' ? 'change' : 'input';
                el.addEventListener(evt, this.debounce(() => {
                    this.runRealtimeValidation();
                }, 300));
            });

            // 初始运行一次（空表单提示不会过度打扰，仅记录状态）
            this.runRealtimeValidation();
        },

        // 收集当前表单数据（与提交时保持一致）
        collectCurrentOrderData() {
            const form = this.elements.orderForm;
            if (!form) return {};
            const fd = new FormData(form);
            const get = (id) => document.getElementById(id)?.value?.trim() || '';

            const data = {
                customer_name: get('customerName'),
                customer_contact: get('customerContact'),
                customer_email: get('customerEmail'),
                pickup: get('pickup'),
                destination: get('dropoff'),
                date: get('pickupDate'),
                time: get('pickupTime'),
                passenger_number: get('passengerCount'),
                luggage_number: get('luggageCount'),
                ota_reference_number: get('otaReferenceNumber'),
                flight_info: get('flightInfo'),
                sub_category_id: get('subCategoryId'),
                car_type_id: get('carTypeId'),
                driving_region_id: get('drivingRegionId')
            };

            // 负责人（若登录）
            const email = localStorage.getItem('user_email');
            if (email && window.ota?.api?.getBackendUserIdByEmail) {
                // 如果有映射函数
                try { data.incharge_by_backend_user_id = window.ota.api.getBackendUserIdByEmail(email); } catch(_){}
            }

            return data;
        },

        // 执行实时验证
        runRealtimeValidation() {
            if (!window.ota?.api?.validateOrderData) return;
            const orderData = this.collectCurrentOrderData();
            const result = window.ota.api.validateOrderData(orderData);
            this.applyValidationResult(result);
            return result;
        },

        // 将验证结果应用到UI
        applyValidationResult(result) {
            const form = this.elements.orderForm;
            if (!form) return;

            // 字段映射（验证字段 → 表单元素ID）
            const fieldMap = {
                sub_category_id: 'subCategoryId',
                ota_reference_number: 'otaReferenceNumber',
                car_type_id: 'carTypeId',
                incharge_by_backend_user_id: 'inchargeBy',
                customer_email: 'customerEmail',
                date: 'pickupDate',
                passenger_number: 'passengerCount',
                luggage_number: 'luggageCount',
                ota_price: 'otaPrice',
                driver_fee: 'driverFee',
                driver_collect: 'driverCollect'
            };

            // 先清理旧状态
            form.querySelectorAll('.field-error, .field-warning').forEach(n => n.remove());
            form.querySelectorAll('.input-invalid, .input-warning').forEach(el => {
                el.classList.remove('input-invalid','input-warning');
            });

            // 应用错误
            Object.entries(result.errors || {}).forEach(([field, messages]) => {
                const id = fieldMap[field];
                if (!id) return;
                const el = document.getElementById(id);
                if (!el) return;
                el.classList.add('input-invalid');
                const msgEl = document.createElement('div');
                msgEl.className = 'field-error';
                msgEl.textContent = messages.join('；');
                el.parentNode?.appendChild(msgEl);
            });

            // 应用警告（不阻挡提交）
            if (result.warnings?.length) {
                // 目前只有电话号码格式警告 -> 尝试映射
                const phoneInput = document.getElementById('customerContact');
                if (phoneInput) {
                    phoneInput.classList.add('input-warning');
                    const wEl = document.createElement('div');
                    wEl.className = 'field-warning';
                    wEl.textContent = result.warnings.join('；');
                    phoneInput.parentNode?.appendChild(wEl);
                }
            }

            // 汇总信息
            let summary = form.querySelector('.validation-summary');
            if (!summary) {
                summary = document.createElement('div');
                summary.className = 'validation-summary';
                form.appendChild(summary);
            }
            if (!result.isValid) {
                summary.className = 'validation-summary error';
                summary.textContent = `❌ 存在 ${Object.keys(result.errors).length} 项必填错误，无法提交`;
            } else if (result.warnings?.length) {
                summary.className = 'validation-summary warning';
                summary.textContent = `⚠️ ${result.warnings.length} 项警告（可继续编辑或直接提交）`;
            } else {
                summary.className = 'validation-summary';
                summary.textContent = '✅ 所有必填字段已通过验证';
            }
        },
        
        // 设置图片上传功能
        setupImageUpload() {
            // 🚀 修复：使用正确的元素ID
            const imageUploadBtn = document.getElementById('imageUploadButton');
            const imageInput = document.getElementById('imageFileInput');
            
            if (!imageUploadBtn || !imageInput) {
                console.warn('⚠️ 图片上传元素未找到:', { 
                    uploadBtn: !!imageUploadBtn, 
                    fileInput: !!imageInput 
                });
                return;
            }
            
            imageUploadBtn.addEventListener('click', () => imageInput.click());
            imageInput.addEventListener('change', (e) => this.handleImageUpload(e));
            
            // 支持拖拽上传
            const dropArea = this.elements.orderInput;
            if (dropArea) {
                dropArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    dropArea.classList.add('drag-over');
                });
                
                dropArea.addEventListener('dragleave', () => {
                    dropArea.classList.remove('drag-over');
                });
                
                dropArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    dropArea.classList.remove('drag-over');
                    
                    const files = Array.from(e.dataTransfer.files);
                    const imageFiles = files.filter(f => f.type.startsWith('image/'));
                    
                    if (imageFiles.length > 0) {
                        this.processImageFile(imageFiles[0]);
                    }
                });
            }
        },
        
        createImageUploadButton() {
            const btn = document.createElement('button');
            btn.id = 'imageUploadBtn';
            btn.type = 'button';
            btn.innerHTML = '📷 上传图片';
            btn.className = 'btn btn-secondary';
            
            // 插入到订单输入区域附近
            const inputSection = this.elements.orderInput?.parentNode;
            if (inputSection) {
                inputSection.appendChild(btn);
            }
            
            return btn;
        },
        
        createImageInput() {
            const input = document.createElement('input');
            input.id = 'imageInput';
            input.type = 'file';
            input.accept = 'image/*';
            input.style.display = 'none';
            document.body.appendChild(input);
            return input;
        },
        
        async handleImageUpload(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            await this.processImageFile(file);
        },
        
        async processImageFile(file) {
            this.showMessage('正在分析图片...', 'info');
            
            try {
                const result = await window.ota.api.uploadAndAnalyzeImage(file);
                
                if (result.isMultiOrder) {
                    window.ota.multiOrder.activate(result.orders, '图片解析结果');
                } else if (result.order) {
                    this.fillOrderForm(result.order);
                }
                
                this.showSuccess('图片分析完成');
                
            } catch (error) {
                console.error('图片分析失败:', error);
                this.showError('图片分析失败，请尝试其他图片或手动输入');
            }
        },
        
        // 订单表单提交处理
        async handleOrderSubmit(event) {
            event.preventDefault();
            const formData = new FormData(event.target); // 重新采集
            const orderData = Object.fromEntries(formData);
            
            try {
                this.showMessage('正在创建订单...', 'info');
                
                const result = await window.ota.api.createOrder(orderData);
                
                this.showSuccess('订单创建成功！');
                
                // 保存到历史
                const userEmail = localStorage.getItem('user_email');
                if (userEmail) {
                    window.ota.history.save(orderData, userEmail);
                }
                
                // 清空表单
                event.target.reset();
                
            } catch (error) {
                console.error('订单创建失败:', error);
                this.showError(`订单创建失败: ${error.message}`);
            }
        },
        
        // 直接处理流水线 - 消除事件驱动复杂性
        async analyzeInput(text) {
            if (!text.trim() || text.length < 20) return;
            
            try {
                // 步骤1: 检测多订单
                if (window.ota.multiOrder.detect(text)) {
                    const result = await window.ota.gemini.parseOrder(text);
                    if (result.isMultiOrder) {
                        // 直接激活多订单模式 - 无事件派发
                        return window.ota.multiOrder.activate(result.orders, text);
                    }
                }
                
                // 步骤2: 单订单处理流水线
                const channel = window.ota.channelDetector.detect(text);
                const result = await window.ota.gemini.parseOrder(text, channel);
                
                // 步骤3: 直接填充表单 - 无事件链
                this.fillOrderForm(result);
                
            } catch (error) {
                console.error('订单分析失败:', error);
                this.showError('订单分析失败，请检查输入格式');
            }
        },
        
        // 直接表单填充 - 无事件派发
        fillOrderForm(orderData) {
            const fields = {
                'customerName': orderData.customer_name,
                'customerContact': orderData.customer_contact,
                'customerEmail': orderData.customer_email,
                'pickup': orderData.pickup,
                'dropoff': orderData.destination,
                'pickupDate': orderData.date,
                'pickupTime': orderData.time,
                'passengerCount': orderData.passenger_number,
                'otaReferenceNumber': orderData.ota_reference_number,
                'flightInfo': orderData.flight_info
            };
            
            Object.entries(fields).forEach(([fieldId, value]) => {
                const element = document.getElementById(fieldId);
                if (element && value) {
                    element.value = value;
                    // 酒店名称标准化
                    if (fieldId === 'pickup' || fieldId === 'dropoff') {
                        element.value = window.ota.hotels.normalize(value);
                    }
                }
            });
            
            console.log('✅ 订单表单已填充');
            this.showSuccess('订单信息已自动填充');
        },
        
        // 直接显示消息 - 无事件系统
        showSuccess(message) {
            this.showMessage(message, 'success');
        },
        
        showError(message) {
            this.showMessage(message, 'error');
        },
        
        showMessage(message, type = 'info') {
            // 简单的消息显示，替代复杂的事件系统
            const existingMessage = document.querySelector('.ota-message');
            if (existingMessage) {
                existingMessage.remove();
            }
            
            const messageEl = document.createElement('div');
            messageEl.className = `ota-message ota-message-${type}`;
            messageEl.textContent = message;
            messageEl.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 9999;
                padding: 12px 20px; border-radius: 4px; color: white;
                background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
                box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            `;
            
            document.body.appendChild(messageEl);
            
            // 3秒后自动消失
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.remove();
                }
            }, 3000);
        },
        
        // 工具方法
        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    }
};

// 自动初始化核心系统
document.addEventListener('DOMContentLoaded', () => {
    window.ota.ui.init();
    console.log('✅ OTA核心系统已初始化 - Linus Torvalds式重构版本');
});
