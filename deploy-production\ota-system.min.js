'use strict';window.ota = {config:{api:{baseURL:'https:timeout:30000 },gemini:{endpoint:'https:timeout:45000 }},history:{storageKey:'ota_order_history',maxSize:1000,save(order,userEmail) {const key = `${this.storageKey}_${userEmail}`;const history = JSON.parse(localStorage.getItem(key) || '[]');order.created_at = new Date().toISOString();order.id = order.id || Date.now().toString();history.unshift(order);if (history.length > this.maxSize) {history.splice(this.maxSize);}localStorage.setItem(key,JSON.stringify(history));console.log(`✅ 订单已保存到历史:${order.id}`);},get(userEmail,limit = 50) {const key = `${this.storageKey}_${userEmail}`;const history = JSON.parse(localStorage.getItem(key) || '[]');return history.slice(0,limit);},clear(userEmail) {const key = `${this.storageKey}_${userEmail}`;localStorage.removeItem(key);}},hotels:{data:[ {name:'香格里拉酒店',english:'Shangri-La Hotel Kuala Lumpur',region:'KL' },{name:'希尔顿酒店',english:'Hilton Kuala Lumpur',region:'KL' },{name:'万豪酒店',english:'JW Marriott Kuala Lumpur',region:'KL' },{name:'丽思卡尔顿酒店',english:'The Ritz-Carlton Kuala Lumpur',region:'KL' },{name:'洲际酒店',english:'InterContinental Kuala Lumpur',region:'KL' },{name:'槟城香格里拉酒店',english:'Shangri-La Hotel Penang',region:'Penang' },{name:'东方大酒店',english:'Eastern & Oriental Hotel',region:'Penang' },{name:'马六甲香格里拉酒店',english:'Shangri-La Hotel Malacca',region:'Malacca' }],find(query) {const q = query.toLowerCase();return this.data.filter(hotel => hotel.name.includes(q) || hotel.english.toLowerCase().includes(q) );},normalize(hotelName) {const found = this.find(hotelName);return found.length > 0 ? found[0].english :hotelName;}},channelDetector:{patterns:{fliggy:/订单编号[：:\s]*\d{19}/,jingge:/jingge|jinggeshop|精格|精格商铺/i,reference:/^(CD|CT|KL|KK)[A-Z0-9]{6,12}$/i },detect(text) {const results = [];if (this.patterns.fliggy.test(text)) {results.push({channel:'fliggy',confidence:0.9 });}if (this.patterns.jingge.test(text)) {results.push({channel:'jingge',confidence:0.85 });}if (this.patterns.reference.test(text)) {results.push({channel:'generic',confidence:0.7 });}return results.length > 0 ? results[0] :null;}},gemini:{async parseOrder(text,channel = null) {const prompt = this.buildPrompt(text,channel);const apiKey = window.GEMINI_API_KEY;if (!apiKey) {throw new Error('Gemini API密钥未配置');}try {const response = await fetch(`${window.ota.config.gemini.endpoint}?key=${apiKey}`,{method:'POST',headers:{'Content-Type':'application/json' },body:JSON.stringify({contents:[{parts:[{text:prompt }] }],generationConfig:{temperature:0.1,maxOutputTokens:4096 }}),signal:AbortSignal.timeout(window.ota.config.gemini.timeout) });if (!response.ok) {throw new Error(`Gemini API错误:${response.status}`);}const data = await response.json();const resultText = data.candidates?.[0]?.content?.parts?.[0]?.text;if (!resultText) {throw new Error('Gemini返回空结果');}return JSON.parse(resultText);}catch (error) {console.error('Gemini API调用失败:',error);throw error;}},buildPrompt(text,channel) {let basePrompt = `请解析以下订单文本，返回JSON格式的订单信息：\n\n${text}\n\n`;if (channel) {basePrompt += `检测到的渠道:${channel.channel}\n`;}basePrompt += ` 要求： 1. 如果包含多个订单，设置 "isMultiOrder":true，并在 "orders" 数组中返回所有订单 2. 如果只有一个订单，设置 "isMultiOrder":false，在 "order" 中返回订单信息 3. 必须提取：customer_name,customer_contact,pickup,destination,date,time,passenger_number 4. 可选字段：customer_email,ota_reference_number,flight_info,sub_category_id,driving_region_id 返回格式示例： {"isMultiOrder":false,"order":{"customer_name":"张三","customer_contact":"+60123456789","pickup":"吉隆坡国际机场","destination":"市中心酒店","date":"2025-08-15","time":"14:00","passenger_number":2 }}`;return basePrompt;}},api:{async request(endpoint,options = {}) {const url = `${window.ota.config.api.baseURL}${endpoint}`;const token = localStorage.getItem('access_token');const config = {method:options.method || 'GET',headers:{'Content-Type':'application/json',...(token && {'Authorization':`Bearer ${token}` }),...options.headers },signal:AbortSignal.timeout(window.ota.config.api.timeout) };if (options.body) {config.body = JSON.stringify(options.body);}try {const response = await fetch(url,config);if (!response.ok) {throw new Error(`API错误:${response.status}${response.statusText}`);}return await response.json();}catch (error) {console.error(`API请求失败 [${endpoint}]:`,error);throw error;}},async createOrder(orderData) {return this.request('/v1/order-jobs',{method:'POST',body:orderData });},async getOrderHistory(params = {}) {const query = new URLSearchParams(params).toString();return this.request(`/v1/order-jobs?${query}`);},async getCoreData() {const [subCategories,carTypes,regions] = await Promise.all([ this.request('/v1/sub-categories'),this.request('/v1/car-types'),this.request('/v1/driving-regions') ]);return {subCategories,carTypes,regions };}},multiOrder:{isActive:false,orders:[],originalText:'',detect(text) {const orderPatterns = [ /订单[编号]*[：:\s]*[\d\w]+/gi,/\d{2}:\d{2}.*?\d{2}:\d{2}/g,/(\d{1,2}月\d{1,2}日|\d{4}-\d{2}-\d{2})/g ];const matches = orderPatterns.map(pattern => (text.match(pattern) || []).length );const totalMatches = matches.reduce((a,b) => a + b,0);return totalMatches >= 3;},activate(orders,originalText) {this.isActive = true;this.orders = orders;this.originalText = originalText;console.log(`✅ 多订单模式已激活:${orders.length}个订单`);this.showUI();},showUI() {const panel = document.getElementById('multiOrderPanel');if (!panel) return;const ordersHTML = this.orders.map((order,index) => ` <div class="multi-order-item" data-index="${index}"> <input type="checkbox" checked class="order-checkbox"> <div class="order-summary"> <strong>${order.customer_name || '未知客户'}</strong> <span>${order.date}${order.time}</span> <span>${order.pickup}→ ${order.destination}</span> </div> </div> `).join('');panel.innerHTML = ` <h3>检测到多个订单 (${this.orders.length})</h3> <div class="multi-order-list">${ordersHTML}</div> <div class="multi-order-actions"> <button onclick="window.ota.multiOrder.createSelected()">创建选中订单</button> <button onclick="window.ota.multiOrder.createAll()">创建全部订单</button> <button onclick="window.ota.multiOrder.cancel()">取消</button> </div> `;panel.classList.remove('hidden');},async createSelected() {const checkboxes = document.querySelectorAll('.order-checkbox:checked');const selectedOrders = Array.from(checkboxes).map(cb => {const index = parseInt(cb.closest('.multi-order-item').dataset.index);return this.orders[index];});await this.batchCreate(selectedOrders);},async createAll() {await this.batchCreate(this.orders);},async batchCreate(orders) {console.log(`🚀 开始批量创建 ${orders.length}个订单`);const results = [];for (let i = 0;i < orders.length;i++) {const order = orders[i];try {console.log(`📝 创建订单 ${i + 1}/${orders.length}:${order.customer_name}`);const result = await window.ota.api.createOrder(order);results.push({success:true,order,result });const userEmail = localStorage.getItem('user_email');if (userEmail) {window.ota.history.save(order,userEmail);}}catch (error) {console.error(`❌ 订单创建失败:${order.customer_name}`,error);results.push({success:false,order,error });}if (i < orders.length - 1) {await new Promise(resolve => setTimeout(resolve,500));}}this.showResults(results);},showResults(results) {const successCount = results.filter(r => r.success).length;const failCount = results.length - successCount;alert(`批量创建完成:\n✅ 成功:${successCount}\n❌ 失败:${failCount}`);this.cancel();},cancel() {this.isActive = false;this.orders = [];this.originalText = '';const panel = document.getElementById('multiOrderPanel');if (panel) {panel.classList.add('hidden');}console.log('✅ 多订单模式已取消');}},ui:{elements:{},init() {this.elements = {orderInput:document.getElementById('orderInput'),workspace:document.getElementById('workspace'),multiOrderPanel:document.getElementById('multiOrderPanel'),orderForm:document.getElementById('orderForm') };this.bindEvents();console.log('✅ UI初始化完成');},bindEvents() {if (this.elements.orderInput) {this.elements.orderInput.addEventListener('input',this.debounce((e) => this.analyzeInput(e.target.value),1000) );}},async analyzeInput(text) {if (!text.trim() || text.length < 20) return;try {if (window.ota.multiOrder.detect(text)) {const result = await window.ota.gemini.parseOrder(text);if (result.isMultiOrder) {return window.ota.multiOrder.activate(result.orders,text);}}const channel = window.ota.channelDetector.detect(text);const result = await window.ota.gemini.parseOrder(text,channel);this.fillOrderForm(result);}catch (error) {console.error('订单分析失败:',error);this.showError('订单分析失败，请检查输入格式');}},fillOrderForm(orderData) {const fields = {'customerName':orderData.customer_name,'customerContact':orderData.customer_contact,'customerEmail':orderData.customer_email,'pickup':orderData.pickup,'dropoff':orderData.destination,'pickupDate':orderData.date,'pickupTime':orderData.time,'passengerCount':orderData.passenger_number,'otaReferenceNumber':orderData.ota_reference_number,'flightInfo':orderData.flight_info };Object.entries(fields).forEach(([fieldId,value]) => {const element = document.getElementById(fieldId);if (element && value) {element.value = value;if (fieldId === 'pickup' || fieldId === 'dropoff') {element.value = window.ota.hotels.normalize(value);}}});console.log('✅ 订单表单已填充');this.showSuccess('订单信息已自动填充');},showSuccess(message) {this.showMessage(message,'success');},showError(message) {this.showMessage(message,'error');},showMessage(message,type = 'info') {const existingMessage = document.querySelector('.ota-message');if (existingMessage) {existingMessage.remove();}const messageEl = document.createElement('div');messageEl.className = `ota-message ota-message-${type}`;messageEl.textContent = message;messageEl.style.cssText = ` position:fixed;top:20px;right:20px;z-index:9999;padding:12px 20px;border-radius:4px;color:white;background:${type === 'success' ? '#4CAF50' :type === 'error' ? '#f44336' :'#2196F3'};box-shadow:0 2px 5px rgba(0,0,0,0.2);`;document.body.appendChild(messageEl);setTimeout(() => {if (messageEl.parentNode) {messageEl.remove();}},3000);},debounce(func,wait) {let timeout;return function executedFunction(...args) {const later = () => {clearTimeout(timeout);func(...args);};clearTimeout(timeout);timeout = setTimeout(later,wait);};}}};document.addEventListener('DOMContentLoaded',() => {window.ota.ui.init();console.log('✅ OTA核心系统已初始化 - Linus Torvalds式重构版本');});'use strict';window.OTA = window.OTA || {};window.OTA.adapters = window.OTA.adapters || {};window.BaseManager = class BaseManager {constructor(name) {this.name = name || 'UnknownManager';this.initialized = false;}async init() {this.initialized = true;return true;}log(message,level = 'info') {console.log(`[${this.name}] ${message}`);}logError(message,error) {console.error(`[${this.name}] ${message}`,error);}};window.OTA.adapters.GeminiServiceAdapter = {async parseOrder(text,channel = null) {return window.ota.gemini.parseOrder(text,channel);},standardizeFields(input) {const mapping = {customerName:'customer_name',customerContact:'customer_contact',customerEmail:'customer_email',pickupDate:'date',pickupTime:'time',pickup:'pickup',dropoff:'destination',passengerCount:'passenger_number',luggageCount:'luggage_number',otaReferenceNumber:'ota_reference_number',flightInfo:'flight_info' };const result = {};Object.entries(input).forEach(([key,value]) => {const mappedKey = mapping[key] || key;result[mappedKey] = value;});return result;}};window.OTA.adapters.UIManagerAdapter = {fillForm(data) {window.ota.ui.fillOrderForm(data);},showMultiOrder(orders,text) {window.ota.ui.showMultiOrderMode(orders,text);},resetForm() {window.ota.ui.enhanced.resetForm();}};window.OTA.adapters.MultiOrderManagerAdapter = {async processMultiOrder(orders) {return window.ota.ui.batchCreateOrders(orders);},showMultiOrderInterface(data) {window.ota.ui.showMultiOrderMode(data.orders,data.originalText);}};window.getLogger = function() {return {log:(message,level) => console.log(`[${level?.toUpperCase() || 'INFO'}] ${message}`),logError:(message,error) => console.error(`[ERROR] ${message}`,error) };};window.OTA.container = {get(serviceName) {const serviceMap = {'apiService':window.ota.api,'geminiService':window.ota.gemini,'uiManager':window.ota.ui,'channelDetector':window.ota.channelDetector };return serviceMap[serviceName] || null;}};window.OTA.eventCoordinator = {listeners:new Map(),on(event,callback) {if (!this.listeners.has(event)) {this.listeners.set(event,[]);}this.listeners.get(event).push(callback);},emit(event,data) {const callbacks = this.listeners.get(event) || [];callbacks.forEach(callback => {try {callback(data);}catch (error) {console.error(`事件处理器错误 [${event}]:`,error);}});}};console.log('✅ 兼容性桥接已加载 - 替代适配器废话');'use strict';class SimpleOTAApp {constructor() {this.startTime = performance.now();this.isInitialized = false;}async start() {console.log('🚀 OTA订单处理系统启动中...');try {this.checkDependencies();this.initAuth();this.setupErrorHandling();this.bindGlobalEvents();this.isInitialized = true;const totalTime = performance.now() - this.startTime;console.log(`✅ OTA系统启动完成，总耗时:${totalTime.toFixed(1)}ms`);this.showStartupSuccess(totalTime);}catch (error) {console.error('❌ OTA系统启动失败:',error);this.showStartupError(error);}}checkDependencies() {if (!window.ota) {throw new Error('核心模块未加载');}if (!window.ota.api || !window.ota.gemini || !window.ota.ui) {throw new Error('关键模块缺失');}console.log('✅ 依赖检查通过');}initAuth() {const token = localStorage.getItem('access_token');const userEmail = localStorage.getItem('user_email');if (token && userEmail) {console.log(`✅ 用户已登录:${userEmail}`);this.showMainWorkspace();}else {console.log('🔑 用户未登录，显示登录界面');this.showLoginPanel();}}setupErrorHandling() {window.addEventListener('error',(event) => {console.error('全局错误:',event.error);});window.addEventListener('unhandledrejection',(event) => {console.error('未处理的Promise拒绝:',event.reason);});console.log('✅ 全局错误处理已设置');}bindGlobalEvents() {const loginForm = document.getElementById('loginForm');if (loginForm) {loginForm.addEventListener('submit',(e) => this.handleLogin(e));}const logoutBtn = document.getElementById('logoutBtn');if (logoutBtn) {logoutBtn.addEventListener('click',() => this.handleLogout());}const historyBtn = document.getElementById('historyBtn');if (historyBtn) {historyBtn.addEventListener('click',() => this.showOrderHistory());}console.log('✅ 全局事件已绑定');}async handleLogin(event) {event.preventDefault();const email = document.getElementById('email').value;const password = document.getElementById('password').value;if (!email || !password) {alert('请输入邮箱和密码');return;}try {console.log('🔑 正在登录...');const response = await fetch('https:method:'POST',headers:{'Content-Type':'application/json' },body:JSON.stringify({email,password }) });if (!response.ok) {throw new Error('登录失败');}const data = await response.json();localStorage.setItem('access_token',data.access_token);localStorage.setItem('user_email',email);console.log('✅ 登录成功');this.showMainWorkspace();}catch (error) {console.error('❌ 登录失败:',error);alert('登录失败，请检查邮箱和密码');}}handleLogout() {localStorage.removeItem('access_token');localStorage.removeItem('user_email');console.log('✅ 已退出登录');this.showLoginPanel();}showLoginPanel() {const loginPanel = document.getElementById('loginPanel');const workspace = document.getElementById('workspace');if (loginPanel) loginPanel.classList.remove('hidden');if (workspace) workspace.classList.add('hidden');}showMainWorkspace() {const loginPanel = document.getElementById('loginPanel');const workspace = document.getElementById('workspace');if (loginPanel) loginPanel.classList.add('hidden');if (workspace) workspace.classList.remove('hidden');this.updateUserInfo();}updateUserInfo() {const userEmail = localStorage.getItem('user_email');const currentUserElement = document.getElementById('currentUser');const userInfoElement = document.getElementById('userInfo');if (currentUserElement && userEmail) {currentUserElement.textContent = userEmail;}if (userInfoElement) {userInfoElement.classList.remove('hidden');}}async showOrderHistory() {try {console.log('📋 获取订单历史...');const orders = await window.ota.api.getOrderHistory({limit:50 });console.log(`✅ 获取到 ${orders.length}条订单记录`);}catch (error) {console.error('❌ 获取订单历史失败:',error);alert('获取订单历史失败');}}showStartupSuccess(totalTime) {const connectionStatus = document.getElementById('connectionStatus');const dataStatus = document.getElementById('dataStatus');if (connectionStatus) {connectionStatus.textContent = '🟢 已连接';connectionStatus.className = 'status-item status-connected';}if (dataStatus) {dataStatus.textContent = '📊 数据已加载';dataStatus.className = 'status-item status-ready';}const lastUpdate = document.getElementById('lastUpdate');if (lastUpdate) {lastUpdate.textContent = `⚡ 启动耗时:${totalTime.toFixed(0)}ms`;}}showStartupError(error) {const connectionStatus = document.getElementById('connectionStatus');if (connectionStatus) {connectionStatus.textContent = '🔴 启动失败';connectionStatus.className = 'status-item status-error';}alert(`系统启动失败:${error.message}`);}}const app = new SimpleOTAApp();if (document.readyState === 'loading') {document.addEventListener('DOMContentLoaded',() => app.start());}else {app.start();}window.otaApp = app;console.log('✅ 简化入口文件已加载');