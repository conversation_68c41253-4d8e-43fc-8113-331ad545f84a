/**
 * ============================================================================
 * 🚀 核心业务流程 - 多订单管理器适配器 (兼容性保证)
 * ============================================================================
 *
 * @fileoverview 多订单管理器适配器 - 兼容性保证
 * @description 保持向后兼容性，将旧的多订单管理API调用适配到新的母子两层架构
 * 
 * @businessFlow 多订单管理适配
 * 在核心业务流程中的作用：
 * 旧代码调用 → 【当前文件职责】多订单API适配和转换
 *     ↓
 * 调用新的OrderManagementController → 返回兼容格式的结果
 *
 * @architecture Adapter Layer (适配器层)
 * - 职责：新旧多订单管理架构之间的桥梁
 * - 原则：保持完全的向后兼容性
 * - 接口：提供与原multi-order-manager-v2.js相同的API
 *
 * @dependencies 依赖关系
 * 上游依赖：
 * - 现有代码的multiOrderManager调用
 * 下游依赖：
 * - controllers/order-management-controller.js (新架构)
 * - order/multi-order-handler.js (多订单处理)
 * - order/api-caller.js (API调用)
 * - order/history-manager.js (历史管理)
 *
 * @localProcessing 本地处理职责
 * - 🟢 多订单API调用适配和转换
 * - 🟢 数据格式兼容性处理
 * - 🟢 错误处理和降级方案
 * - 🟢 状态管理和事件转发
 *
 * @remoteProcessing 远程处理职责
 * - 🔴 无远程处理职责 (纯适配器)
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-09
 */

(function() {
    'use strict';

    /**
     * @ADAPTER 多订单管理器适配器
     * 提供与原multi-order-manager-v2.js完全兼容的API接口
     */
    class MultiOrderManagerAdapter {
        constructor() {
            this.logger = window.getLogger(); // 统一日志服务
            // 减法修复：移除立即获取appState，改为延迟获取，避免加载时序问题
            this._appState = null; // 延迟初始化的appState引用

            // 新架构组件引用
            this.orderManagementController = null; // 延迟初始化
            this.multiOrderHandler = null; // 延迟初始化
            this.apiCaller = null; // 延迟初始化
            this.historyManager = null; // 延迟初始化

            // 适配器状态
            this.isInitialized = false;
            this.initializationPromise = null;

            this.logger.log('🔄 MultiOrderManagerAdapter 初始化开始', 'info');
            this.initializeAsync();
        }

        /**
         * 延迟获取AppState实例
         * 减法修复：避免在构造函数中立即获取，解决加载时序问题
         * @returns {Object} AppState实例
         */
        _getAppState() {
            if (!this._appState) {
                this._appState = window.getAppState();
            }
            return this._appState;
        }

        /**
         * 异步初始化新架构组件
         */
        async initializeAsync() {
            if (this.initializationPromise) {
                return this.initializationPromise;
            }

            this.initializationPromise = this._doInitialize();
            return this.initializationPromise;
        }

        async _doInitialize() {
            try {
                // 等待新架构组件加载
                await this._waitForNewArchitecture();
                
                // 获取新架构组件实例
                this.orderManagementController = window.OTA.orderManagementController;
                this.multiOrderHandler = window.OTA.multiOrderHandler;
                this.apiCaller = window.OTA.apiCaller;
                this.historyManager = window.OTA.historyManager;
                
                this.isInitialized = true;
                this.logger.log('✅ MultiOrderManagerAdapter 初始化完成', 'success');
                
                return true;
            } catch (error) {
                this.logger.log(`❌ MultiOrderManagerAdapter 初始化失败: ${error.message}`, 'error');
                return false;
            }
        }

        /**
         * 等待新架构组件加载完成
         */
        async _waitForNewArchitecture() {
            const maxWaitTime = 10000; // 最大等待10秒
            const checkInterval = 100; // 每100ms检查一次
            let waitTime = 0;

            while (waitTime < maxWaitTime) {
                if (window.OTA?.orderManagementController && 
                    window.OTA?.multiOrderHandler &&
                    window.OTA?.apiCaller &&
                    window.OTA?.historyManager) {
                    return true;
                }
                
                await new Promise(resolve => setTimeout(resolve, checkInterval));
                waitTime += checkInterval;
            }
            
            throw new Error('新架构组件加载超时');
        }

        /**
         * 确保适配器已初始化
         */
        async _ensureInitialized() {
            if (!this.isInitialized) {
                await this.initializeAsync();
            }
            
            if (!this.isInitialized) {
                throw new Error('MultiOrderManagerAdapter 未能正确初始化');
            }
        }

        // ============================================================================
        // 🔄 兼容性API方法 - 与原multi-order-manager-v2.js保持一致
        // ============================================================================

        /**
         * 检测和拆分多订单 (兼容性方法)
         * @param {string} text - 输入文本
         * @param {Object} options - 选项参数
         * @returns {Promise<Object>} 多订单检测结果
         */
        async detectAndSplitMultiOrders(text, options = {}) {
            try {
                await this._ensureInitialized();
                
                this.logger.log('🔄 [适配器] 调用多订单检测...', 'info');
                
                // 调用新架构的多订单处理器
                const result = await this.multiOrderHandler.detectAndSplitOrders(text, options);
                
                // 转换为旧格式 (保持兼容性)
                return this._convertToLegacyFormat(result);
                
            } catch (error) {
                this.logger.log(`❌ [适配器] 多订单检测失败: ${error.message}`, 'error');
                
                // 降级方案：返回单订单格式
                return {
                    isMultiOrder: false,
                    orders: [{ text: text, index: 0 }],
                    totalCount: 1,
                    confidence: 0.5,
                    source: 'fallback'
                };
            }
        }

        /**
         * 处理多订单 (兼容性方法)
         * @param {Array} orders - 订单数组
         * @param {Object} options - 处理选项
         * @returns {Promise<Object>} 处理结果
         */
        async processMultiOrders(orders, options = {}) {
            try {
                await this._ensureInitialized();
                
                this.logger.log('🔄 [适配器] 调用多订单处理...', 'info');
                
                // 调用新架构的订单管理控制器
                const result = await this.orderManagementController.processMultipleOrders(orders, options);
                
                return result;
                
            } catch (error) {
                this.logger.log(`❌ [适配器] 多订单处理失败: ${error.message}`, 'error');
                throw error;
            }
        }

        /**
         * 激活多订单模式 (兼容性方法)
         * @param {Array} orders - 订单数组
         * @returns {Promise<Object>} 激活结果
         */
        async activateMultiOrderMode(orders) {
            try {
                await this._ensureInitialized();
                
                this.logger.log('🔄 [适配器] 激活多订单模式...', 'info');
                
                // 调用新架构的多订单处理器
                const result = await this.multiOrderHandler.activateMultiOrderMode(orders);
                
                return result;
                
            } catch (error) {
                this.logger.log(`❌ [适配器] 多订单模式激活失败: ${error.message}`, 'error');
                throw error;
            }
        }

        /**
         * 获取多订单状态 (兼容性方法)
         * @returns {Object} 多订单状态
         */
        getMultiOrderStatus() {
            try {
                if (!this.isInitialized) {
                    return { status: 'not_initialized', orders: [], count: 0 };
                }
                
                // 调用新架构的多订单处理器
                return this.multiOrderHandler.getMultiOrderStatus();
                
            } catch (error) {
                this.logger.log(`❌ [适配器] 获取多订单状态失败: ${error.message}`, 'error');
                return { status: 'error', orders: [], count: 0 };
            }
        }

        /**
         * 创建选中的订单 (兼容性方法)
         * @param {Array} selectedOrders - 选中的订单
         * @returns {Promise<Object>} 创建结果
         */
        async createSelectedOrders(selectedOrders) {
            try {
                await this._ensureInitialized();
                
                this.logger.log('🔄 [适配器] 创建选中订单...', 'info');
                
                // 调用新架构的API调用器
                const result = await this.apiCaller.createMultipleOrders(selectedOrders);
                
                return result;
                
            } catch (error) {
                this.logger.log(`❌ [适配器] 创建选中订单失败: ${error.message}`, 'error');
                throw error;
            }
        }

        // ============================================================================
        // 🔧 内部工具方法
        // ============================================================================

        /**
         * 转换为旧格式 (保持兼容性)
         * @param {Object} newFormatResult - 新格式结果
         * @returns {Object} 旧格式结果
         */
        _convertToLegacyFormat(newFormatResult) {
            // 确保返回格式与旧版本一致
            return {
                isMultiOrder: newFormatResult.isMultiOrder || false,
                orders: newFormatResult.orders || [],
                totalCount: newFormatResult.totalCount || 0,
                confidence: newFormatResult.confidence || 0,
                source: 'new-architecture',
                timestamp: new Date().toISOString(),
                
                // 保持旧版本的额外字段
                metadata: newFormatResult.metadata || {},
                processingTime: newFormatResult.processingTime || 0
            };
        }

        /**
         * 获取适配器状态
         * @returns {Object} 适配器状态信息
         */
        getAdapterStatus() {
            return {
                isInitialized: this.isInitialized,
                hasOrderManagementController: !!this.orderManagementController,
                hasMultiOrderHandler: !!this.multiOrderHandler,
                hasAPICaller: !!this.apiCaller,
                hasHistoryManager: !!this.historyManager,
                timestamp: new Date().toISOString()
            };
        }

        // ============================================================================
        // 🎯 UI交互方法 (兼容性支持)
        // ============================================================================

        /**
         * 显示多订单面板 (兼容性方法)
         * @param {Array} orders - 订单数组
         * @param {Object} options - 显示选项
         */
        showMultiOrderPanel(orders, options = {}) {
            try {
                this.logger.log('显示多订单面板', 'info', { orderCount: orders.length });

                // 更新状态
                this.state.parsedOrders = orders;
                this.state.isMultiOrderMode = true;

                // 获取多订单渲染器
                const renderer = window.OTA?.MultiOrderRenderer;
                if (renderer) {
                    const rendererInstance = new renderer();
                    rendererInstance.showMultiOrderPanel(orders);
                } else {
                    this.logger.log('多订单渲染器未找到', 'warning');
                }

                return { success: true, orderCount: orders.length };

            } catch (error) {
                this.logger.logError('显示多订单面板失败', error);
                return { success: false, error: error.message };
            }
        }

        /**
         * 快速编辑订单 - 从多订单模式切换到单订单编辑
         * @param {number} orderIndex - 订单索引
         */
        quickEditOrder(orderIndex) {
            try {
                this.logger.log('开始快速编辑订单', 'info', { orderIndex });

                // 获取订单数据
                const order = this.state.parsedOrders[orderIndex];
                if (!order) {
                    throw new Error(`订单索引 ${orderIndex} 无效`);
                }

                // 隐藏多订单面板
                const multiOrderPanel = document.getElementById('multiOrderPanel');
                if (multiOrderPanel) {
                    multiOrderPanel.classList.add('hidden');
                    multiOrderPanel.style.display = 'none';
                }

                // 映射订单数据到单订单表单
                this.mapOrderToSingleForm(order);

                // 显示返回多订单按钮
                this.showReturnToMultiOrderButton();

                this.logger.log('快速编辑订单完成', 'success', {
                    customerName: order.customerName || order.customer_name
                });

            } catch (error) {
                this.logger.logError('快速编辑订单失败', error);
            }
        }

        /**
         * 显示返回多订单按钮
         */
        showReturnToMultiOrderButton() {
            const returnBtn = document.getElementById('returnToMultiOrder');
            if (returnBtn) {
                returnBtn.classList.remove('hidden');
                returnBtn.style.display = 'inline-block';
                this.logger.log('返回多订单按钮已显示', 'info');
            } else {
                this.logger.log('返回多订单按钮元素未找到', 'warning');
            }
        }

        /**
         * 映射订单数据到单订单表单
         * @param {Object} order - 订单数据
         */
        mapOrderToSingleForm(order) {
            try {
                // 获取表单管理器
                const formManager = window.OTA?.uiManager?.getManager('form');
                if (formManager && formManager.populateFormWithOrderData) {
                    formManager.populateFormWithOrderData(order);
                    this.logger.log('订单数据已通过表单管理器映射到表单', 'success');
                    return;
                }

                // 后备方案：直接操作DOM
                this.mapOrderToSingleFormFallback(order);

            } catch (error) {
                this.logger.logError('映射订单数据到表单失败', error);
                this.mapOrderToSingleFormFallback(order);
            }
        }

        /**
         * 映射订单数据到单订单表单 - 后备方案
         * @param {Object} order - 订单数据
         */
        mapOrderToSingleFormFallback(order) {
            try {
                // 字段映射配置
                const fieldMappings = {
                    'customerName': order.customerName || order.customer_name || '',
                    'customerContact': order.customerContact || order.customer_contact || '',
                    'customerEmail': order.customerEmail || order.customer_email || '',
                    'pickup': order.pickup || order.pickup_location || '',
                    'dropoff': order.dropoff || order.dropoff_location || '',
                    'pickupDate': order.pickupDate || order.pickup_date || '',
                    'pickupTime': order.pickupTime || order.pickup_time || '',
                    'passengerCount': order.passengerCount || order.passenger_count || 1,
                    'luggageCount': order.luggageCount || order.luggage_count || 0,
                    'otaPrice': order.otaPrice || order.ota_price || '',
                    'otaReferenceNumber': order.otaReferenceNumber || order.ota_reference_number || '',
                    'extraRequirement': order.extraRequirement || order.extra_requirement || ''
                };

                // 填充基础表单字段
                Object.entries(fieldMappings).forEach(([fieldId, value]) => {
                    const element = document.getElementById(fieldId);
                    if (element) {
                        element.value = value;
                        // 触发change事件以确保其他监听器能响应
                        element.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                });

                // 设置车型选择
                const carTypeId = order.carTypeId || order.car_type_id;
                if (carTypeId) {
                    const carTypeSelect = document.getElementById('carType');
                    if (carTypeSelect) {
                        carTypeSelect.value = carTypeId;
                        carTypeSelect.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                }

                // 设置语言选择
                const languagesArray = order.languagesIdArray || order.languages_id_array || [];
                if (languagesArray.length > 0) {
                    languagesArray.forEach(langId => {
                        const checkbox = document.querySelector(`input[name="languagesIdArray"][value="${langId}"]`);
                        if (checkbox) {
                            checkbox.checked = true;
                            checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                        }
                    });
                }

                this.logger.log('订单数据已通过后备方案映射到表单', 'success');

            } catch (error) {
                this.logger.logError('后备方案映射订单数据失败', error);
            }
        }

        /**
         * 切换订单选择状态 (兼容性方法)
         * @param {number} orderIndex - 订单索引
         */
        toggleOrderSelection(orderIndex) {
            try {
                // 这个方法主要用于多订单面板中的选择框交互
                // 实际的选择逻辑由多订单渲染器处理
                this.logger.log('切换订单选择状态', 'info', { orderIndex });

                // 可以在这里添加额外的选择状态处理逻辑

            } catch (error) {
                this.logger.logError('切换订单选择状态失败', error);
            }
        }
    }

    // ============================================================================
    // 🌐 全局注册和暴露
    // ============================================================================

    // 等待OTA命名空间准备就绪
    if (typeof window.OTA === 'undefined') {
        window.OTA = {};
    }

    // 创建适配器实例
    const multiOrderManagerAdapter = new MultiOrderManagerAdapter();

    // 注册到OTA命名空间
    window.OTA.MultiOrderManagerAdapter = MultiOrderManagerAdapter;
    window.OTA.multiOrderManagerAdapter = multiOrderManagerAdapter;

    // 兼容性接口 - 保持与旧版本相同的调用方式
    window.OTA.multiOrderManager = multiOrderManagerAdapter;
    window.multiOrderManager = multiOrderManagerAdapter;
    
    // 工厂函数 (兼容性)
    window.getMultiOrderManager = function() {
        return multiOrderManagerAdapter;
    };

    console.log('✅ MultiOrderManagerAdapter 已加载并注册');
})();
