/* 多订单卡片样式 - 桌面端样式（移动端样式已迁移到 css/multi-order/mobile.css） */

/* 
 * 注意：CSS变量已在 base/variables.css 中统一定义
 * 移动端样式已迁移到 css/multi-order/mobile.css
 */

/* 多订单面板基础样式 */
.multi-order-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: var(--overlay-backdrop);
    z-index: var(--z-toast); /* 使用最高层级，确保在所有元素之上 */
    overflow-y: auto;
    padding: 16px;
    box-sizing: border-box;
    display: none; /* 默认隐藏 */
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.multi-order-panel:not(.hidden) {
    display: flex !important;
}

.multi-order-content {
    max-width: 1200px;
    width: 90vw;
    margin: 20px auto;
    background: var(--bg-tertiary);
    -webkit-backdrop-filter: var(--blur-glass);
    backdrop-filter: var(--blur-glass);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    position: relative;
}

/* 多订单组件专用样式 - 使用全局变量支持暗色模式 */
.multi-order-panel,
.multi-order-content,
.order-card {
  /* 组件使用全局变量，无需重复定义 */
}

/* 多订单头部样式 - 使用新色彩系统 */
.multi-order-header {
    background: var(--brand-gradient);
    color: var(--color-white);
    padding: var(--spacing-sm) var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 50px; /* 按设计文档要求 */
}

.multi-order-header h3 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
}

/* 头部左侧区域 */
.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.btn-header-back {
    background: var(--button-overlay-light);
    border: 1px solid var(--button-overlay-medium);
    color: var(--color-white);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-sm);
    border-radius: 6px;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.btn-header-back:hover {
    background: var(--button-overlay-medium);
    transform: translateX(-2px);
}

.btn-header-action {
    background: var(--button-overlay-strong);
    border: 1px solid var(--button-overlay-strong);
    color: var(--color-primary);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-sm);
    font-weight: 600;
    border-radius: 6px;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.btn-header-action:hover {
    background: var(--color-white);
    transform: translateY(-1px);
}

/* 筛选排序控制栏 - 按设计文档要求40px高度 */
.filter-sort-controls {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-sm) var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 40px;
    -webkit-backdrop-filter: var(--blur-glass);
    backdrop-filter: var(--blur-glass);
}

.filter-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.control-label {
    font-size: var(--font-sm);
    font-weight: 600;
    color: var(--text-accent);
    white-space: nowrap;
}

.filter-select {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-tertiary);
    color: var(--text-primary);
    font-size: var(--font-sm);
    min-width: 80px;
    cursor: pointer;
}

.filter-select:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px var(--brand-glass);
}

.view-toggle {
    background: var(--brand-glass);
    border: 1px solid var(--color-primary);
    color: var(--text-accent);
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-sm);
    border-radius: 4px;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.view-toggle.active {
    background: var(--color-primary);
    color: var(--color-white);
}

.quick-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.quick-btn {
    background: var(--brand-glass);
    border: 1px solid var(--color-primary);
    color: var(--text-accent);
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-xs);
    border-radius: 4px;
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
}

.quick-btn:hover {
    background: var(--color-primary);
    color: var(--color-white);
    transform: translateY(-1px);
}

.multi-order-controls {
    display: flex;
    align-items: center;
    gap: 16px;
}

.order-stats {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
}

.order-count {
    font-weight: 600;
    font-size: 1rem;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.header-actions .btn {
    padding: 6px 12px;
    font-size: 0.9rem;
    border-radius: 6px;
}

/* 移动端样式已迁移到 css/multi-order/mobile.css */

/* ========================================
   订单卡片网格容器优化
   ======================================== */

/* 订单卡片网格 - 桌面端布局 */
.multi-order-list {
    padding: var(--spacing-2);
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-3);
    background: var(--bg-primary);
    -webkit-backdrop-filter: var(--blur-glass);
    backdrop-filter: var(--blur-glass);
    min-height: 300px;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    border-radius: 0;
}

/* 多订单底部样式 - 重新设计为两层布局 */
.multi-order-footer {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
    -webkit-backdrop-filter: var(--blur-glass);
    backdrop-filter: var(--blur-glass);
    border-top: 1px solid var(--button-overlay-medium);
    border-radius: 0 0 16px 16px;
    position: sticky;
    bottom: 0;
    z-index: 10;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
    padding: var(--spacing-3);
}

.footer-actions-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-2);
    flex-wrap: wrap;
}

/* 底部操作按钮分组 */
.footer-actions-left,
.footer-actions-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.footer-actions-center {
    flex: 1;
    text-align: center;
}

/* Footer按钮样式 - 与导航栏颜色同步 */
.btn-footer {
    background: var(--button-overlay-light);
    border: 1px solid var(--button-overlay-medium);
    color: var(--color-white);
    padding: 2px 6px; /* 减少30%：原本更大的padding */
    font-size: 0.85rem; /* 减少15%：1rem → 0.85rem */
    border-radius: 4px;
    transition: all var(--transition-fast);
}

.btn-footer:hover {
    background: var(--button-overlay-medium);
    transform: translateY(-1px);
}

.btn-footer-primary {
    background: var(--button-overlay-strong);
    border: 1px solid var(--button-overlay-strong);
    color: var(--color-primary);
    padding: 2px 8px; /* 略大一点作为主要按钮 */
    font-size: 0.85rem; /* 减少15% */
    font-weight: 600;
    border-radius: 4px;
    transition: all var(--transition-fast);
}

.btn-footer-primary:hover {
    background: var(--color-white);
    transform: translateY(-1px) scale(1.02);
}

.footer-count {
    color: var(--color-white);
    font-size: 0.85rem; /* 减少15% */
    font-weight: 500;
    white-space: nowrap;
    padding: 0 4px;
}

/* 紧凑卡片设计 - 按设计文档要求，减少15%高度 */
.order-card {
    background: var(--bg-tertiary);
    border-radius: 12px;
    box-shadow: var(--shadow-card);
    transition: all var(--transition-normal) ease;
    overflow: hidden;
    border: 1px solid var(--border-color);
    position: relative;
    min-height: calc(80px * 0.85); /* 减少15%高度：80px → 68px */
    -webkit-backdrop-filter: var(--blur-glass);
    backdrop-filter: var(--blur-glass);
}

.order-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px -5px var(--brand-overlay-strong);
    border-color: var(--color-primary);
}

.order-card.selected {
    border-color: var(--color-primary);
    background: linear-gradient(135deg, var(--brand-glass) 0%, var(--brand-overlay-subtle) 100%);
}

.order-card.paging-order {
    border-left: 4px solid var(--color-warning-yellow);
}

/* 紧凑流式布局样式 - 修改为按行布局自动换行 */
.compact-inline-layout {
    --item-height: 20px;
    --item-spacing: 1px;
    --font-size: 11px;
    --line-height: 1.2;
    display: flex;
    flex-wrap: wrap;
    gap: var(--item-spacing);
    align-items: flex-start;
    justify-content: flex-start;
    padding: 2px;
    width: 100%;
    box-sizing: border-box;
    min-height: var(--item-height);
}

.inline-item {
    display: inline-flex;
    align-items: center;
    height: var(--item-height);
    margin-right: var(--item-spacing);
    font-size: var(--font-size);
    line-height: var(--line-height);
    white-space: nowrap;
}

/* 字段容器样式 - 流式布局优化 */
.grid-item {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 20px;
    padding: 1px;
    background: var(--bg-card);
    border: 1px solid var(--color-primary);
    border-radius: 3px;
    font-size: 11px;
    line-height: 1.2;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    transition: all var(--transition-fast) ease;
    cursor: pointer;
    width: fit-content;
    white-space: nowrap;
    flex: none;
    box-sizing: border-box;
    min-width: 0;
}

/* 地址字段特殊样式 - 流式布局优化 */
.grid-item-route {
    height: 20px;
    min-height: 20px;
    width: fit-content;
    
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    align-items: center;
    padding: 0 4px;
    flex: 0 0 auto;
}

.grid-item-route .grid-value {
    font-size: 11px;
    line-height: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
    width: 100%;
    
}

/* 地址字段特殊样式 - 已简化为统一样式 */

.grid-item:hover {
    border-color: var(--color-primary-dark);
    box-shadow: 0 2px 6px rgba(159, 41, 159, 0.2);
    transform: translateY(-1px);
}

/* 字段宽度分类 - 已移除固定宽度，使用fit-content */

/* 字段内部元素样式 - 统一定义 */
.grid-value {
    font-weight: 500;
    color: var(--text-primary);
    white-space: nowrap;
    text-align: center;
    
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: inherit;
    line-height: inherit;
}

/* 地址字段特殊处理 - 已合并到上面的定义中 */

/* 只读字段样式 */
.grid-item:not(.editable-field) {
    cursor: default;
    opacity: 0.8;
    border-color: var(--border-color);
}

.grid-item:not(.editable-field):hover {
    transform: none;
    border-color: var(--border-color);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.inline-label {
    font-weight: 600;
    margin-right: var(--spacing-xs);
    color: var(--color-primary);
}

.inline-value {
    color: var(--text-secondary);
}

/* 状态图标样式 */
.status-icon {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: var(--spacing-xs);
    vertical-align: middle;
}

.status-ready { color: var(--color-success-green); }
.status-warning { color: var(--color-warning-amber); }
.status-error { color: var(--color-error-red); }
.status-processing { color: var(--color-info-blue); }
.status-completed { color: var(--color-success-green); }
.status-cancelled { color: var(--color-error-red); }
.status-complete { color: var(--color-success-green); }
.status-progress { color: var(--color-warning-amber); }
.status-cancel { color: var(--color-error-red); }
.status-normal { color: var(--color-success-green); }

/* 卡片头部 */
.order-card-header {
    padding: calc(4px * 0.85); /* 减少15%：4px → 3.4px */
    background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-primary) 100%);
    color: var(--color-white);
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: calc(28px * 0.85); /* 减少15%：28px → 23.8px */
}

.order-selector {
    display: flex;
    align-items: center;
    gap: 2px;
}

.order-checkbox {
    width: 12px;
    height: 12px;
    accent-color: var(--color-white);
}

.order-title {
    display: flex;
    align-items: center;
    gap: 2px;
}

.order-number {
    font-weight: 600;
    font-size: 14px;
}

.paging-badge {
    background: var(--warning-bg-overlay);
    color: var(--color-warning-yellow);
    padding: 1px 2px;
    border-radius: 2px;
    font-size: 8px;
    font-weight: 500;
    line-height: 18px;
    height: 18px;
    display: flex;
    align-items: center;
}

.order-status {
    display: flex;
    align-items: center;
}

.status-badge {
    padding: 1px 3px;
    border-radius: 3px;
    font-size: 8px;
    font-weight: 500;
    line-height: 18px;
    height: 20px;
    display: flex;
    align-items: center;
}

.status-ready {
    background: var(--success-bg-overlay);
    color: var(--color-success-green);
}

/* 卡片主体 */
.order-card-body {
    padding: calc(4px * 0.85) calc(8px * 0.85); /* 减少15% */
    cursor: pointer;
}

.order-card-body:hover {
    background: var(--color-primary-bg);
}

/* 订单详情区域 */
.order-details {
    display: flex;
    flex-direction: row; /* 改为横向流式 */
    flex-wrap: wrap;
    gap: 1px; /* 统一1px间距 */
    font-size: calc(1rem * 0.85); /* 减少15%字体大小 */
}

/* 基础信息行 */
.order-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 4px;
}

.order-customer {
    font-weight: 600;
    color: var(--text-primary);
    font-size: calc(0.9rem * 0.85); /* 减少15% */
}

.order-contact {
    color: var(--text-secondary);
    font-size: calc(0.8rem * 0.85); /* 减少15% */
}

/* 路线信息行 */
.order-route {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: calc(0.85rem * 0.85); /* 减少15% */
}

.order-pickup, .order-dropoff {
    flex: 1;
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.order-arrow {
    color: var(--color-primary);
    font-weight: bold;
}

/* 元数据行 */
.order-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 4px;
    font-size: calc(0.8rem * 0.85); /* 减少15% */
}

.order-date, .order-time {
    color: var(--text-accent);
}

.order-price {
    font-weight: 600;
    color: var(--color-primary);
}

/* 新增字段区域 - 使用色块区分 */
.order-additional {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-top: calc(4px * 0.85); /* 减少15% */
    padding: calc(2px * 0.85) 0; /* 减少15% */
    border-top: 1px solid var(--border-color);
}

/* OTA渠道色块 */
.order-ota {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    color: #1565c0;
    padding: calc(2px * 0.85) calc(6px * 0.85); /* 减少15% */
    border-radius: 4px;
    font-size: calc(0.75rem * 0.85); /* 减少15% */
    font-weight: 500;
    border: 1px solid #90caf9;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 80px;
}

/* 车型色块 */
.order-vehicle {
    background: linear-gradient(135deg, #f3e5f5, #e1bee7);
    color: #7b1fa2;
    padding: calc(2px * 0.85) calc(6px * 0.85); /* 减少15% */
    border-radius: 4px;
    font-size: calc(0.75rem * 0.85); /* 减少15% */
    font-weight: 500;
    border: 1px solid #ce93d8;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 80px;
}

/* 驾驶区域色块 */
.order-region {
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
    color: #2e7d32;
    padding: calc(2px * 0.85) calc(6px * 0.85); /* 减少15% */
    border-radius: 4px;
    font-size: calc(0.75rem * 0.85); /* 减少15% */
    font-weight: 500;
    border: 1px solid #a5d6a7;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 80px;
}

/* 需求语言色块 */
.order-language {
    background: linear-gradient(135deg, #fff3e0, #ffe0b2);
    color: #f57c00;
    padding: calc(2px * 0.85) calc(6px * 0.85); /* 减少15% */
    border-radius: 4px;
    font-size: calc(0.75rem * 0.85); /* 减少15% */
    font-weight: 500;
    border: 1px solid #ffcc02;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 80px;
}

/* 卡片底部操作区 - 已移除 */

/* 订单卡片操作按钮样式 - 已移除 */

.btn-icon {
    font-size: 16px;
    opacity: 0.9;
}

.btn-text {
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* ========================================
   订单布局样式 - 已改为流式布局（重复定义已移除）
   ======================================== */

/* 可编辑字段样式 */
.editable-field {
    cursor: pointer;
    background: linear-gradient(135deg, var(--brand-glass) 0%, var(--brand-overlay-minimal) 100%);
}

.editable-field:hover {
    background: linear-gradient(135deg, var(--brand-overlay-subtle) 0%, var(--brand-overlay-medium) 100%);
    box-shadow: 0 2px 8px var(--brand-overlay-extra);
}

.editable-field:active {
    transform: translateY(0);
    box-shadow: inset 0 2px 4px var(--brand-overlay-medium);
}

/* 网格标签样式 */
.grid-label {
    font-size: 16px;
    min-width: 20px;
    text-align: center;
    flex-shrink: 0;
    opacity: 0.8;
}

/* 网格值样式 - 已删除重复定义，使用第432行的统一定义 */

/* 编辑指示器 */
.edit-indicator {
    font-size: 12px;
    opacity: 0;
    transition: opacity var(--transition-fast);
    margin-left: auto;
    flex-shrink: 0;
}

.editable-field:hover .edit-indicator {
    opacity: 0.7;
}

/* 路线显示特殊样式 - 已移除grid-column冲突 */

.route-display {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 0px;
}

.pickup-address,
.dropoff-address {
    display: flex;
    align-items: center;
    gap: 1px;
    font-size: 9px;
    line-height: 10px;
    height: 10px;
}

.address-label {
    font-weight: 600;
    color: var(--color-primary);
    min-width: 20px;
    flex-shrink: 0;
    font-size: 8px;
    line-height: 10px;
}

.address-text {
    color: var(--text-secondary);
    line-height: 10px;
    word-break: break-word;
    font-size: 8px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

/* 动画效果 */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.order-card {
    animation: slideIn 0.3s ease-out;
}

/* 关闭按钮样式 */
.multi-order-close-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    background: var(--overlay-backdrop);
    color: var(--color-white);
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 18px;
    z-index: 1002;
    transition: all var(--transition-fast) ease;
}

.multi-order-close-btn:hover {
    background: var(--shadow-darker);
}

/* 紧凑按钮样式 */
.btn-compact {
    padding: 1px 3px;
    font-size: 9px;
    height: 20px;
    min-height: 20px;
    max-height: 20px;
    min-width: 30px;
    border-radius: 3px;
    border: 1px solid var(--color-primary);
    background: var(--brand-gradient);
    color: var(--color-white);
    cursor: pointer;
    transition: all var(--transition-fast) ease;
    margin-right: 1px;
    line-height: 18px;
}

.btn-compact:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px var(--brand-overlay-extra);
}

/* 三列移动端布局 */
.three-column-mobile {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 4px;
    height: calc(100vh - 70px);
    overflow: hidden;
    padding: 4px;
}

.column-mobile {
    overflow-y: auto;
    padding: 4px;
    border-radius: 8px;
    background: var(--brand-overlay-minimal);
}

/* 滚动条样式已在 base/reset.css 中统一定义 */

/* 字段编辑器样式 */
.field-editor {
    width: 100%;
    padding: 4px 8px;
    border: 1px solid var(--color-primary);
    border-radius: 4px;
    background: var(--bg-card);
    color: var(--text-primary);
    font-size: 12px;
    font-family: inherit;
    outline: none;
    transition: all var(--transition-fast) ease;
}

.field-editor:focus {
    border-color: var(--color-primary);
    background: var(--bg-primary);
    box-shadow: 0 0 0 2px var(--brand-overlay-subtle);
}

.field-editor:hover:not(:focus) {
    border-color: var(--color-primary);
    background: var(--bg-secondary);
}

/* 下拉选择器样式 */
.field-editor select {
    width: 100%;
    padding: 4px 8px;
    border: 1px solid var(--color-primary);
    border-radius: 4px;
    background: var(--bg-card);
    color: var(--text-primary);
    font-size: 12px;
    font-family: inherit;
    outline: none;
    cursor: pointer;
    transition: all var(--transition-fast) ease;
}

.field-editor select:focus {
    border-color: var(--color-primary);
    background: var(--bg-primary);
    box-shadow: 0 0 0 2px var(--brand-overlay-subtle);
}

.field-editor select:hover:not(:focus) {
    border-color: var(--color-primary);
    background: var(--bg-secondary);
}

/* 编辑状态样式 */
.grid-item.editing {
    background: var(--brand-overlay-subtle);
    border: 1px solid var(--color-primary);
    box-shadow: 0 0 8px var(--brand-overlay-medium);
}
