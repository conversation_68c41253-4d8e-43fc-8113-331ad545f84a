<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA订单处理系统 - 生产环境</title>
    <link rel="stylesheet" href="css/main.css">
    
    
</head>
<body>
    

    <div id="app">
        <!-- 登录面板 -->
        <div id="loginPanel" class="login-panel">
            <div class="login-card">
                <h2>🚗 OTA订单处理系统</h2>
                <p>Linus Torvalds式重构版本</p>
                <form id="loginForm">
                    <div class="form-group">
                        <input type="email" id="email" placeholder="邮箱" required>
                    </div>
                    <div class="form-group">
                        <input type="password" id="password" placeholder="密码" required>
                    </div>
                    <button type="submit" class="btn btn-primary">登录</button>
                </form>
            </div>
        </div>

        <!-- 主工作区 -->
        <div id="workspace" class="hidden">
            <header class="app-header">
                <h1>OTA订单处理系统</h1>
                <div class="user-info">
                    <span id="currentUser"></span>
                    <button id="logoutBtn" class="btn btn-outline">退出登录</button>
                </div>
            </header>

            <main class="main-content">
                <div class="input-section">
                    <h3>订单信息输入</h3>
                    <textarea id="orderInput" 
                              placeholder="请粘贴订单信息，系统将自动解析..."
                              rows="6"></textarea>
                </div>

                <div class="form-section">
                    <h3>订单详情</h3>
                    <form id="orderForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="customerName">客户姓名</label>
                                <input type="text" id="customerName" required>
                            </div>
                            <div class="form-group">
                                <label for="customerContact">联系电话</label>
                                <input type="tel" id="customerContact" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="pickup">接送地点</label>
                                <input type="text" id="pickup" required>
                            </div>
                            <div class="form-group">
                                <label for="dropoff">目的地</label>
                                <input type="text" id="dropoff" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="pickupDate">接送日期</label>
                                <input type="date" id="pickupDate" required>
                            </div>
                            <div class="form-group">
                                <label for="pickupTime">接送时间</label>
                                <input type="time" id="pickupTime" required>
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">创建订单</button>
                            <button type="button" id="resetBtn" class="btn btn-secondary">重置</button>
                        </div>
                    </form>
                </div>
            </main>
        </div>

        <!-- 多订单面板 -->
        <div id="multiOrderPanel" class="hidden">
            <div class="multi-order-container">
                <h3>多订单处理</h3>
                <div id="multiOrderList"></div>
                <div class="multi-order-actions">
                    <button onclick="window.ota.multiOrder.createSelected()" class="btn btn-primary">创建选中</button>
                    <button onclick="window.ota.multiOrder.createAll()" class="btn btn-success">创建全部</button>
                    <button onclick="window.ota.multiOrder.cancel()" class="btn btn-secondary">取消</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 性能监控 (非生产环境) -->
    

    <!-- 脚本加载 -->
    <script>
        const ENVIRONMENT = 'production';
        const DEBUG = false;
        const startTime = performance.now();
        
        
    </script>
    
    
    <script src="ota-system.min.js"></script>
    
    
    
</body>
</html>